-- Drop the primary key constraint if it exists
ALTER TABLE public.user_actions DROP CONSTRAINT IF EXISTS user_actions_pkey;

-- Create the sequence
CREATE SEQUENCE IF NOT EXISTS user_actions_userid_seq;

-- Alter the column to BIGINT and set the default value to the sequence
ALTER TABLE public.user_actions ALTER COLUMN userid TYPE BIGINT;
ALTER TABLE public.user_actions ALTER COLUMN userid SET DEFAULT nextval('user_actions_userid_seq');

-- Reapply the primary key constraint
ALTER TABLE public.user_actions ADD CONSTRAINT user_actions_pkey PRIMARY KEY (userid);



ALTER TABLE public.clicks_and_purchases
ADD COLUMN created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP;


SELECT 
    DATE_TRUNC('month', action_time) AS month,
    COUNT(*) AS action_count
FROM 
    public.clicks_and_purchases cap 
GROUP BY 
    month
ORDER BY 
    month;


CREATE TABLE users (
    user_id SERIAL PRIMARY KEY,  -- Auto-incrementing BIGINT ID
    userid VARCHAR(255) NOT NULL UNIQUE
);

ALTER TABLE itemdata.items
ADD COLUMN listing_status int2 NULL;

-- Create the parent table, partitioned by RANGE on item_id
CREATE TABLE items (
    item_id int8 PRIMARY KEY,
    category_id int4,
    price numeric(10, 2) NULL,
    quantity_sold int4 NULL,
	quantity_available int4 null,
    start_time TIMESTAMP NOT NULL,
    found_time TIMESTAMP,    
    end_time TIMESTAMP,
    update_time timestamp NULL,
    duration int4    
) PARTITION BY RANGE (item_id);

-- Create partitions based on the provided ranges

CREATE TABLE items_0_to_125 PARTITION OF items
    FOR VALUES FROM (0) TO (125000000000);

CREATE TABLE items_125_to_135 PARTITION OF items
    FOR VALUES FROM (125000000001) TO (135000000000);

CREATE TABLE items_135_to_145 PARTITION OF items
    FOR VALUES FROM (135000000001) TO (145000000000);

CREATE TABLE items_145_to_155 PARTITION OF items
    FOR VALUES FROM (145000000001) TO (155000000000);

CREATE TABLE items_155_to_165 PARTITION OF items
    FOR VALUES FROM (155000000001) TO (165000000000);

CREATE TABLE items_165_to_175 PARTITION OF items
    FOR VALUES FROM (165000000001) TO (175000000000);

CREATE TABLE items_175_to_185 PARTITION OF items
    FOR VALUES FROM (175000000001) TO (185000000000);

CREATE TABLE items_185_to_195 PARTITION OF items
    FOR VALUES FROM (185000000001) TO (195000000000);

CREATE TABLE items_195_to_204 PARTITION OF items
    FOR VALUES FROM (195000000001) TO (204000000000);

CREATE TABLE items_204_to_225 PARTITION OF items
    FOR VALUES FROM (204000000001) TO (225000000000);

CREATE TABLE items_225_to_235 PARTITION OF items
    FOR VALUES FROM (225000000001) TO (235000000000);

CREATE TABLE items_235_to_255 PARTITION OF items
    FOR VALUES FROM (235000000001) TO (255000000000);

CREATE TABLE items_255_to_265 PARTITION OF items
    FOR VALUES FROM (255000000001) TO (265000000000);

CREATE TABLE items_265_to_275 PARTITION OF items
    FOR VALUES FROM (265000000001) TO (275000000000);

CREATE TABLE items_275_to_285 PARTITION OF items
    FOR VALUES FROM (275000000001) TO (285000000000);

CREATE TABLE items_285_to_295 PARTITION OF items
    FOR VALUES FROM (285000000001) TO (295000000000);

CREATE TABLE items_295_to_305 PARTITION OF items
    FOR VALUES FROM (295000000001) TO (305000000000);

CREATE TABLE items_305_to_315 PARTITION OF items
    FOR VALUES FROM (305000000001) TO (315000000000);

CREATE TABLE items_315_to_325 PARTITION OF items
    FOR VALUES FROM (315000000001) TO (325000000000);

CREATE TABLE items_325_to_335 PARTITION OF items
    FOR VALUES FROM (325000000001) TO (335000000000);

CREATE TABLE items_335_to_355 PARTITION OF items
    FOR VALUES FROM (335000000001) TO (355000000000);

CREATE TABLE items_355_to_365 PARTITION OF items
    FOR VALUES FROM (355000000001) TO (365000000000);

CREATE TABLE items_365_to_375 PARTITION OF items
    FOR VALUES FROM (365000000001) TO (375000000000);

CREATE TABLE items_375_to_385 PARTITION OF items
    FOR VALUES FROM (375000000001) TO (385000000000);

CREATE TABLE items_385_to_395 PARTITION OF items
    FOR VALUES FROM (385000000001) TO (395000000000);

CREATE TABLE items_395_to_405 PARTITION OF items
    FOR VALUES FROM (395000000001) TO (405000000000);

CREATE TABLE items_405_to_415 PARTITION OF items
    FOR VALUES FROM (405000000001) TO (415000000000);

CREATE TABLE items_415_to_900 PARTITION OF items
    FOR VALUES FROM (415000000001) TO (900000000000);