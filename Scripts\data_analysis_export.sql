SELECT conname, conrelid::regclass AS table_name
FROM pg_constraint
WHERE conname LIKE '%item_predict_pkey%';




SELECT 
    i.search_name,
    i.item_id,
    i.category_id,
    i.title,
    i.price,
    i.shipping_price,
    i.shipping_type,
    i.shipping_cost_paid_by_option,
    i.dispatch_time_max,
    i.country_from,
    i.country_ships_to,
    i.postal_code,
    i.condition_id,
    CASE 
        WHEN i.condition_id = 1000 THEN 'New'
        WHEN i.condition_id = 1500 THEN 'New other'
        WHEN i.condition_id = 1750 THEN 'New with defects'
        WHEN i.condition_id = 2000 THEN 'Manufacturer refurbished'
        WHEN i.condition_id = 2010 THEN 'Excellent Refurbished'
        WHEN i.condition_id = 2020 THEN 'Very Good Refurbished'
        WHEN i.condition_id = 2030 THEN 'Good Refurbished'
        WHEN i.condition_id = 2500 THEN 'Seller refurbished'
        WHEN i.condition_id = 2750 THEN 'Like new'
        WHEN i.condition_id = 3000 THEN 'Used'
        WHEN i.condition_id = 4000 THEN 'Very good'
        WHEN i.condition_id = 5000 THEN 'Good'
        WHEN i.condition_id = 6000 THEN 'Acceptable'
        WHEN i.condition_id = 7000 THEN 'For parts or not working'
        ELSE 'Unspecified'
    END AS condition_name,
    i.duration_seconds,
    i.sub_title,
    i.condition_description,
    i.description,
    i.specifics,
    i.brand,
    i.model,
    i.mpn,
    i.upc,
    i.product_reference_id,
    i.seller_name,
    i.seller_feedback_count,
    i.seller_feedback_percent,    
    s.store_name AS seller_store_name,
    s.registration_date AS seller_registration_date,
    s.user_web_country_id AS seller_user_web_country_id,
    s.user_api_country_id AS seller_user_api_country_id,
    s.store_web_country_id AS seller_store_web_country_id,
    s.store_api_country_id AS seller_store_api_country_id,
    s.transaction_percent AS seller_transaction_percent,
    s.store_owner AS seller_store_owner,
    s.business AS seller_business,
    s.top_rated AS seller_top_rated,
    s.feedback_private AS seller_feedback_private,
    s.verified AS seller_verified,    
    i.buyer_name,
    i.buyer_feedback_count,
    i.buyer_feedback_percent,
    i.buyer_feedback_private,
    i.buyer_protection,
    i.quantity_total,
    i.quantity_sold,
    i.lot_size,
    i.bid_count,
    i.picture_count,
    i.best_offer,
    i.get_it_fast,
    i.relist_parent_id,
    i.refund_option,
    i.returns_accepted_option,
    i.returns_within_option,
    i.revised,
    i.top_rated_listing,
    i.sold_as_bin,
    i.bin_sold,
    i.auction_sold,
    i.bin_not_sold,
    i.auction_not_sold,
    i.best_offer_sold,
    i.commit_to_buy,
    i.listing_type,
    i.pictures_url,
    i.time_posted,
    i.time_ended,
    i.updated_at
FROM 
    data_analysis.item_predict AS i
LEFT JOIN 
    seller.seller_info AS s
ON 
    i.seller_name = s.username;
   
   
   
   
   
   
   
   