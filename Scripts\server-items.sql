--count by day
SELECT 
    start_datetime::date AS post_date, 
    COUNT(*) AS item_count
FROM 
    public.server_items
WHERE 
    start_datetime IS NOT null
    and start_datetime > '2024-10-01'
GROUP BY 
    start_datetime::date
ORDER BY 
    post_date;

--count by day with median found time   
   SELECT 
    start_datetime::date AS post_date, 
    COUNT(*) AS item_count,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY found_time) AS median_found_time,
     round( AVG(found_time)) AS average_found_time
FROM 
    public.server_items
WHERE 
    start_datetime IS NOT null 
    --and  start_datetime > '2024-10-01'
GROUP BY 
    start_datetime::date
ORDER BY 
    post_date;
