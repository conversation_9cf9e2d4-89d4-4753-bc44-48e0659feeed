
--for each epn item find sold_item match 
SELECT
    et.*,
    si.leaf_category_id,
    si.start_time,
    si.end_time,
    si.price,
    si.quantity_sold,
    si.duration,
    si.buyer_name,
    si.buyer_feedback_count,
    si.listing_status,
    si.fetch_time   
FROM
    public.epn_transactions et 
LEFT JOIN public.sold_items si ON et.itemid = si.item_id
where et.eventdate >'2024-01-01' and et.customid like '%R27WP89a4mYG3GXXyEykoiZziL0%' 
ORDER by et.eventdate desc ;



--  items from epn is missing in sold items based on itemid in category
SELECT e.itemid, e.customid, s.buyer_name, s.duration,s.listing_status  ,s.price , s.start_time, s.leaf_category_id 
FROM public.epn_transactions e
LEFT JOIN public.sold_items s ON e.itemid = s.item_id AND e.leafcategoryid = s.leaf_category_id
WHERE e.leafcategoryid = 111422 and s.listing_status=2;

 