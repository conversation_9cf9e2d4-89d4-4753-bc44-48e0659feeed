--Top Category report with epn sales
--<PERSON><PERSON> calculates total sales, sale counts, and EPN sales by category within a specified date range
SELECT
  si.leaf_category_id,
  c."name" AS category_name,
  SUM(si.price * si.quantity_sold) AS total_sales,
  COUNT(si.price) AS sale_count,
  COALESCE(et.total_epn_sales, 0) AS total_epn_sales -- Use COALESCE to handle categories without EPN sales
  --percentile_cont(0.5) WITHIN GROUP (ORDER BY si.duration) AS median_duration -- Calculate median duration
  --,percentile_cont(0.8) WITHIN GROUP (ORDER BY si.price desc) AS median_duration -- Calculate median duration
FROM
  public.sold_items si
JOIN
  public.categories c ON si.leaf_category_id = c.id
LEFT JOIN (
  SELECT
    leafcategoryid,
    SUM(sales) AS total_epn_sales
  FROM
    public.epn_transactions
  WHERE
    DATE(eventdate) BETWEEN '2024-02-05' AND '2024-03-03'
  GROUP BY
    leafcategoryid
) et ON si.leaf_category_id = et.leafcategoryid
WHERE
  si.end_time::date BETWEEN '2024-02-05' AND '2024-02-11' AND
  si.price IS NOT NULL AND
  si.quantity_sold IS NOT NULL AND
  si.listing_status = 2 AND
  si.duration < 3600
GROUP BY
  si.leaf_category_id,
  c."name",
  et.total_epn_sales
ORDER BY  
  total_sales DESC;

  
 
 

