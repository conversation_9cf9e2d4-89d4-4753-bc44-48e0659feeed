--Count by day
  
select
	<PERSON><PERSON><PERSON>(end_time ) as end_time,
	COUNT(end_time) as total_rows,
	count(listing_status) as listing_status
from
	public.sold_items
where	
	end_time >= '2024-09-01 08:00:00' and end_time < '2025-02-05 08:00:00'
	and 	leaf_category_id in (261328)
	and duration < 600	
	and listing_type=2
group by
	DATE(end_time)
order by
	end_time;
  
--count by month
SELECT
    DATE_TRUNC('month', "end_time") AS month,
    COUNT("end_time") AS total_rows
FROM
    "sold_items"
WHERE
    "end_time" >= '2024-11-01 08:00:00' AND "end_time" < '2025-02-05 08:00:00'
    AND "leaf_category_id" IN (183454)
    AND "duration" < 3600
    AND "listing_status" = 2
GROUP BY
    DATE_TRUNC('month', "end_time")
ORDER BY
    month;
  
--Get item ids under duration 3600 sec
SELECT
    item_id 
FROM
    "sold_items"
WHERE
    "end_time" >= '2024-11-01 08:00:00' AND "end_time" < '2025-02-05 08:00:00'
   -- AND "leaf_category_id" IN (183454)
     AND "leaf_category_id" IN (261328)
    AND "duration" < 3600
    AND "listing_status" = 2


  
  SELECT "leaf_category_id", SUM("quantity_sold" * "price") AS "total_amount"
FROM "sold_items"
WHERE "leaf_category_id" IN (212, 170135, 261332, 261333, 261331, 262055, 183436, 183439, 183442, 261949, 183437, 183441, 183440, 183438, 261334, 261329, 261330, 261328, 261893, 261335)
AND "listing_status" = 2
AND "end_time" >= '2024-12-01' AND "end_time" < '2025-01-01'
GROUP BY "leaf_category_id";



 SELECT si."item_id", 
           si."leaf_category_id", 
           si."price", 
           si."quantity_sold", 
           si."buyer_name", 
           si."buyer_feedback_count", 
           si."duration", 
           si."fetch_time"           
    FROM "public"."sold_items" si     
    WHERE si."fetch_time" > '2024-09-02' 
          AND si."leaf_category_id" = 261332 
          AND si."listing_status" = 2 
          AND si."buyer_name" != '' 
          AND si."buyer_feedback_count" IS NOT NULL 
    ORDER BY si."fetch_time" 
    
    
    
SELECT si.item_id, si.leaf_category_id, si.price, si.quantity_sold, 123si.buyer_name, si.buyer_feedback_count, si.duration, si.fetch_time, CASE WHEN et.itemid IS NOT NULL THEN TRUE ELSE FALSE END AS epn_transaction 
FROM public.sold_items si LEFT JOIN public.epn_transactions et ON si.item_id = et.itemid WHERE si.fetch_time > '2024-09-02' and si.fetch_time < '2024-09-03' AND si.leaf_category_id = 183454
 AND si.listing_status = 2 AND si.buyer_name != '' AND si.buyer_feedback_count IS NOT NULL ORDER BY si.fetch_time
 
 
 
 --count by field, duration <3600
 SELECT 
    jsonb_object_keys(specifics) AS field_name,
    COUNT(*) AS count
FROM 
    "data_analysis"."_2025_02_04_183454_ccg"
GROUP BY 
    field_name
ORDER BY 
    count DESC


  SELECT leaf_category_id, MAX(item_id) AS max_item_id FROM sold_items where item_id>403786946824 GROUP BY leaf_category_id ORDER BY leaf_category_id LIMIT 100  ;
  SELECT leaf_category_id, MAX(start_time) AS max_time FROM sold_items where start_time >'2025-04-12' GROUP BY leaf_category_id ORDER BY leaf_category_id LIMIT 50;