
--<PERSON>, missing in epn, present in sold_items
-- Use MCP database
USE mcp;

SELECT 
    si.*, 
    et.customid AS "customid",
    CASE WHEN et.itemid IS NOT NULL THEN 'TRUE' ELSE 'FALSE' END AS "EPN"
FROM "sold_items" si
LEFT JOIN epn_transactions et ON si."item_id" = et.itemid 
WHERE si.listing_status = 2
AND si.buyer_name = 'l_' 
-- Filter for specific customid pattern in the results that have matches
-- (this won't filter out records missing from epn_transactions)
AND (et.customid LIKE '%931A-721A%' OR et.customid IS NULL)
--AND (si.buyer_feedback_count >8400 and si.buyer_feedback_count <8500 or si.buyer_feedback_count =0)
--AND si."leaf_category_id" IN (124603, 42894, 78192, 181717, 181714, 78191, 57520, 181709, 181708)
--and si.leaf_category_id =16204
ORDER BY end_time DESC
