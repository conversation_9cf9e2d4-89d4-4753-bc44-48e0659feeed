--match epn, seer, and sold items by itemid, with duplicates
select
    si.leaf_category_id, 
    si.item_id,
    et.customid,
    si.duration,
    si.buyer_name,
    si.buyer_feedback_count,     
    case
        when svr.item_id is null then 'NOT Seer'
        else 'Seer'
    end as match_indicator,
    si.start_time,
    svr.found_time,    
    si.end_time,    
    et.eventdate,
    svr.item_id as item_match_in_items    
from
    public.sold_items si
left join public.server_items svr on
    si.item_id = svr.item_id
left join public.epn_transactions et on
    si.item_id = et.itemid
left join public.user_actions at on
    si.item_id = at.item_id
where
    si.duration < 300
    and si.leaf_category_id = 177
order by si.buyer_name;

--match epn, seer, and sold items
SELECT DISTINCT ON (si.item_id)
    si.item_id,
    et.customid,
    si.buyer_feedback_count,
    si.buyer_name,
    CASE
        WHEN svr.item_id IS NULL THEN 'NOT Seer'
        ELSE 'Seer'
    END as match_indicator,
    si.start_time,
    si.duration,
    svr.found_time,
    at.action_datetime,
    si.end_time,    
    et.eventdate,
    svr.item_id as item_match_in_items,
    at.action_datetime
FROM
    public.sold_items si
LEFT JOIN public.server_items svr ON si.item_id = svr.item_id
LEFT JOIN public.epn_transactions et ON si.item_id = et.itemid
LEFT JOIN public.user_actions at ON si.item_id = at.item_id
WHERE
    si.leaf_category_id = 111422
    --and si.duration < 600 
  and si.buyer_name = 'is'
ORDER BY si.item_id;



--export for aggregated buyers
select
    si.item_id,
    si.end_time,
    si.leaf_category_id,
    si.price,
    si.quantity_sold,
    si.buyer_name,
    si.buyer_feedback_count,
    si.duration,
    si.fetch_time,
    case
        when et.itemid is not null then true
        else false
    end as epn_transaction
from
    public.sold_items si
left join public.epn_transactions et on
    si.item_id = et.itemid
where
    si.fetch_time > '2024-02-05'
    --and si.listing_status = 2
    and si.buyer_name != '';
