-- total ebay&epn sales by category
SELECT
  si.leaf_category_id,
  c."name" AS category_name,  
  SUM(si.price * si.quantity_sold) AS total_sales,
  COUNT(si.price) AS sale_count,
  COALESCE(et.total_epn_sales, 0) AS total_epn_sales -- Use COALESCE to handle categories without EPN sales
FROM
  public.sold_items si
JOIN
  public.categories c ON si.leaf_category_id = c.id
LEFT JOIN (
  SELECT
    leafcategoryid,
    SUM(sales) AS total_epn_sales
  FROM
    public.epn_transactions
  WHERE
    DATE(eventdate) BETWEEN '2024-02-05' AND '2024-02-11' 
  GROUP BY
    leafcategoryid
) et ON si.leaf_category_id = et.leafcategoryid
WHERE
  si.end_time::date BETWEEN '2024-02-05' AND '2024-02-11' AND
  si.price IS NOT NULL AND
  si.quantity_sold IS NOT NULL AND
  si.listing_status = 2 and
  si.buyer_feedback_count >400 AND
  si.duration < 86400
GROUP BY
  si.leaf_category_id,
  c."name",
  et.total_epn_sales
ORDER BY  
  total_sales DESC;
  
 
 
 
 ---
 select
	si.leaf_category_id,
	c."name" as category_name,
	SUM(si.price * si.quantity_sold) as total_sales,
	COUNT(si.price) as sale_count,
	--  
	-- Round the percentage of total sales for feedback count <= 400
	ROUND(100.0 * SUM(case when si.buyer_feedback_count >0 and si.buyer_feedback_count <= 400 then si.price * si.quantity_sold else 0 end) / SUM(si.price * si.quantity_sold)) as People_GMV_pct,
	-- Round the percentage of total sales for feedback count > 400
	ROUND(100.0 * SUM(case when si.buyer_feedback_count = 0 or si.buyer_feedback_count > 400 then si.price * si.quantity_sold else 0 end) / SUM(si.price * si.quantity_sold)) as Company_GMV_pct,	
	-- Round the percentage of sale count for feedback count <= 400
	ROUND(100.0 * COUNT(case when si.buyer_feedback_count >0 and si.buyer_feedback_count <= 400 then si.price else null end) / COUNT(si.price)) as People_Count_pct,
	-- Round the percentage of sale count for feedback count > 400
	ROUND(100.0 * COUNT(case when si.buyer_feedback_count = 0 or si.buyer_feedback_count > 400 then si.price else null end) / COUNT(si.price)) as Company_Count_pct,
	--	
    SUM(CASE WHEN si.buyer_feedback_count >0 and si.buyer_feedback_count <= 400 THEN si.price * si.quantity_sold ELSE 0 END) AS People_GMV,
	SUM(case when si.buyer_feedback_count = 0 or si.buyer_feedback_count > 400 then si.price * si.quantity_sold else 0 end) as Company_GMV,	
	COUNT(case when si.buyer_feedback_count >0 and si.buyer_feedback_count <= 400 then si.price else null end) as People_Count,
	COUNT(case when si.buyer_feedback_count = 0 or si.buyer_feedback_count > 400 then si.price else null end) as Company_Count,
	coalesce(et.total_epn_sales,
	0) as total_epn_sales
	-- Use COALESCE to handle categories without EPN sales    
from
	public.sold_items si
join
  public.categories c on
	si.leaf_category_id = c.id
left join (
	select
		leafcategoryid,
		SUM(sales) as total_epn_sales
	from
		public.epn_transactions
	where
		DATE(eventdate) between '2024-02-05' and '2024-02-11'
	group by
		leafcategoryid
) et on
	si.leaf_category_id = et.leafcategoryid
where
	si.end_time::date between '2024-02-05' and '2024-02-11'
	and
  si.price is not null
	and
  si.quantity_sold is not null
	and
  si.listing_status = 2
	and
  si.duration < 300
group by
	si.leaf_category_id,
	c."name",
	et.total_epn_sales
order by
	total_sales desc;

 
 
 
 
 SELECT
  CASE
 WHEN si.buyer_feedback_count BETWEEN 100 AND 199 THEN '199'
    WHEN si.buyer_feedback_count BETWEEN 200 AND 299 THEN '299'
    WHEN si.buyer_feedback_count BETWEEN 300 AND 399 THEN '399'
    WHEN si.buyer_feedback_count BETWEEN 400 AND 499 THEN '499'
    WHEN si.buyer_feedback_count BETWEEN 500 AND 599 THEN '599'
    WHEN si.buyer_feedback_count BETWEEN 600 AND 699 THEN '699'
    WHEN si.buyer_feedback_count BETWEEN 700 AND 799 THEN '799'
    WHEN si.buyer_feedback_count BETWEEN 800 AND 899 THEN '899'
    WHEN si.buyer_feedback_count BETWEEN 900 AND 999 THEN '999'
    WHEN si.buyer_feedback_count BETWEEN 1000 AND 1099 THEN '1099'
    WHEN si.buyer_feedback_count BETWEEN 1100 AND 1199 THEN '1199'
    WHEN si.buyer_feedback_count BETWEEN 1200 AND 1299 THEN '1299'
    WHEN si.buyer_feedback_count BETWEEN 1300 AND 1399 THEN '1399'
    WHEN si.buyer_feedback_count BETWEEN 1400 AND 1499 THEN '1499'
    WHEN si.buyer_feedback_count BETWEEN 1500 AND 1599 THEN '1599'
    WHEN si.buyer_feedback_count BETWEEN 1600 AND 1699 THEN '1699'
    WHEN si.buyer_feedback_count BETWEEN 1700 AND 1799 THEN '1799'
    WHEN si.buyer_feedback_count BETWEEN 1800 AND 1899 THEN '1899'
    WHEN si.buyer_feedback_count BETWEEN 1900 AND 1999 THEN '1999'
    WHEN si.buyer_feedback_count BETWEEN 2000 AND 2099 THEN '2099'
    WHEN si.buyer_feedback_count BETWEEN 2100 AND 2199 THEN '2199'
    WHEN si.buyer_feedback_count BETWEEN 2200 AND 2299 THEN '2299'
    WHEN si.buyer_feedback_count BETWEEN 2300 AND 2399 THEN '2399'
    WHEN si.buyer_feedback_count BETWEEN 2400 AND 2499 THEN '2499'
    WHEN si.buyer_feedback_count BETWEEN 2500 AND 2599 THEN '2599'
    WHEN si.buyer_feedback_count BETWEEN 2600 AND 2699 THEN '2699'
    WHEN si.buyer_feedback_count BETWEEN 2700 AND 2799 THEN '2799'
    WHEN si.buyer_feedback_count BETWEEN 2800 AND 2899 THEN '2899'
    WHEN si.buyer_feedback_count BETWEEN 2900 AND 2999 THEN '2999'
    WHEN si.buyer_feedback_count BETWEEN 3000 AND 3099 THEN '3099'
    -- Add more ranges up to '1900-1999'
    WHEN si.buyer_feedback_count >= 3099  THEN '3099-1000000'
  END AS feedback_range,
  SUM(si.price * si.quantity_sold) AS total_sales
FROM
  public.sold_items si
JOIN
  public.categories c ON si.leaf_category_id = c.id
LEFT JOIN (
  SELECT
    leafcategoryid,
    SUM(sales) AS total_epn_sales
  FROM
    public.epn_transactions
  WHERE
    DATE(eventdate) BETWEEN '2024-02-05' AND '2024-02-11'
  GROUP BY
    leafcategoryid
) et ON si.leaf_category_id = et.leafcategoryid
WHERE
  si.end_time::date BETWEEN '2024-02-05' AND '2024-02-11' AND
  si.price IS NOT NULL AND
  si.quantity_sold IS NOT NULL AND
  si.listing_status = 2 AND
  si.duration < 86400 AND
  si.buyer_feedback_count > 100
GROUP BY
  feedback_range
ORDER BY  
  feedback_range;
 
 
 
 SELECT
  case
	   WHEN si.buyer_feedback_count BETWEEN 0 AND 1000 THEN '999'
    WHEN si.buyer_feedback_count BETWEEN 1000 AND 1999 THEN '1999'
    WHEN si.buyer_feedback_count BETWEEN 2000 AND 2999 THEN '2999'
    WHEN si.buyer_feedback_count BETWEEN 3000 AND 3999 THEN '3999'
    WHEN si.buyer_feedback_count BETWEEN 4000 AND 4999 THEN '4999'
    WHEN si.buyer_feedback_count BETWEEN 5000 AND 5999 THEN '5999'
    WHEN si.buyer_feedback_count BETWEEN 6000 AND 6999 THEN '6999'
    WHEN si.buyer_feedback_count BETWEEN 7000 AND 7999 THEN '7999'
    WHEN si.buyer_feedback_count BETWEEN 8000 AND 8999 THEN '8999'
    WHEN si.buyer_feedback_count BETWEEN 9000 AND 9999 THEN '9999'
    WHEN si.buyer_feedback_count BETWEEN 10000 AND 10999 THEN '10999'
    WHEN si.buyer_feedback_count BETWEEN 11000 AND 11999 THEN '11999'
    WHEN si.buyer_feedback_count BETWEEN 12000 AND 12999 THEN '12999'
    WHEN si.buyer_feedback_count BETWEEN 13000 AND 13999 THEN '13999'
    WHEN si.buyer_feedback_count BETWEEN 14000 AND 14999 THEN '14999'
    WHEN si.buyer_feedback_count BETWEEN 15000 AND 15999 THEN '15999'
    WHEN si.buyer_feedback_count BETWEEN 16000 AND 16999 THEN '16999'
    WHEN si.buyer_feedback_count BETWEEN 17000 AND 17999 THEN '17999'
    WHEN si.buyer_feedback_count BETWEEN 18000 AND 18999 THEN '18999'
    WHEN si.buyer_feedback_count BETWEEN 19000 AND 19999 THEN '19999'
    -- Continue with additional ranges as needed
    WHEN si.buyer_feedback_count >= 20000 THEN '20000+'
  END AS feedback_range,
  SUM(si.price * si.quantity_sold) AS total_sales
FROM
  public.sold_items si
JOIN
  public.categories c ON si.leaf_category_id = c.id
LEFT JOIN (
  SELECT
    leafcategoryid,
    SUM(sales) AS total_epn_sales
  FROM
    public.epn_transactions
  WHERE
    DATE(eventdate) BETWEEN '2024-02-05' AND '2024-02-11'
  GROUP BY
    leafcategoryid
) et ON si.leaf_category_id = et.leafcategoryid
WHERE
  si.end_time::date BETWEEN '2024-02-05' AND '2024-02-11' AND
  si.price IS NOT NULL AND
  si.quantity_sold IS NOT NULL AND
  si.listing_status = 2 AND
  si.duration < 180 AND
  si.buyer_feedback_count >= 0 -- Adjusted to match the new range start
GROUP BY
  feedback_range
ORDER BY  
  feedback_range;

