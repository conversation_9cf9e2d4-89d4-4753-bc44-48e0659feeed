

 
 --Group PlaceOfferWin by licensekey
 SELECT
  --TO_CHAR(action_time, 'YYYY-MM') AS fetch_month,  
  COUNT(*) AS total_rows,
  SUM(amount*0.01) AS total_amount,
  license_key 
FROM
  placeoffer.clicks
WHERE
  action_time >= '2024-09-01'and action_time < '2024-09-17' AND action_type = 'PlaceOfferWin'
GROUP BY
  license_key 
ORDER by 
  total_amount desc ;

--Find duplicate PlaceOfferWin 
 SELECT 
    c.*
FROM 
    placeoffer.clicks c
JOIN 
    (SELECT 
         item_id
     FROM 
         placeoffer.clicks
     WHERE 
         action_type = 'PlaceOfferWin' and action_time > '2024-09-01'
     GROUP BY 
         item_id
     HAVING 
         COUNT(*) > 1) sub
ON 
    c.item_id = sub.item_id
WHERE 
    c.action_type = 'PlaceOfferWin' and action_time > '2024-01-01'


--commit to buy per week
SELECT 
    date_trunc('week', action_time) AS month_start,
    ROUND(SUM(amount), 2) AS total_amount_per_month
FROM 
    placeoffer.clicks
WHERE 
    action_type = 'PlaceOfferWin'
    AND action_time > '2024-06-01'
GROUP BY 
    date_trunc('week', action_time)
ORDER BY 
    month_start;

--Group PlaceOfferWin by month
select
	TO_CHAR(action_time,
	'YYYY-MM') as fetch_month,
	COUNT(*) as total_rows,
	SUM(amount * 0.01) as total_amount
from
	placeoffer.clicks
where
	action_time >= '2024-10-01'
	and action_type = 'PlaceOfferWin'
group by
	fetch_month
order by
	fetch_month;
 
 
 
select
	action_time,
	SUM(amount * 0.09) as total_amount,
	COUNT(*) as total_rows
from
	placeoffer.clicks
where
	action_time >= '2024-09-01'
	and action_time <'2024-10-01'
	and (action_type = 'PaypalWin'
		or action_type = 'PlaceOfferWin')
group by
	action_time
order by
	total_amount asc;
                
   
SELECT
    license_key,
    SUM(amount * 0.009) AS total_amount,
    COUNT(*) AS total_rows                                        
FROM
    placeoffer.clicks
WHERE
  action_time >= '2024-09-01'
    AND action_time <'2024-10-01'
    AND (action_type = 'PaypalWin' OR action_type = 'PlaceOfferWin')
GROUP BY
    license_key
ORDER BY
    total_amount ASC;
                

