-- group summed count by sellername
SELECT 
    "seller_name", 
    SUM("quantity_sold") AS "total_quantity_sold"
FROM 
    "sold_items"
WHERE 
    "seller_name" IN ('wazno', 'trreusegroup', 'retail_king', 'newegg', 'five-star-buy')
    --leaf_category_id =164 
    AND "listing_status" = 0 
    AND "quantity_sold" > 5    
GROUP BY 
    "seller_name";

-- Get seller details
select item_id, leaf_category_id, seller_name, seller_feedback_count 
from sold_items
where seller_name IN ('wazno','trreusegroup', 'retail_king', 'newegg', 'five-star-buy') 
and listing_status = 0;


SELECT 
        username,
        verified,
        feedback_private,
        transaction_percent,
        business,
        store_owner,
        store_name,
        top_rated,
        registration_date,
        user_web_country_id,
        store_web_country_id,
        account_active
      FROM seller.seller_info 
      WHERE username in('decoderworld');


SELECT
    username,
    verified,
    feedback_private,
    transaction_percent,
    business,
    store_owner,
    store_name,
    top_rated,
    registration_date,
    user_web_country_id,
    store_web_country_id,
    account_active
  FROM seller.seller_info
  WHERE lower(username) in ('decoderworld');


CREATE EXTENSION IF NOT EXISTS citext;


-- 2. Drop the old functional index (ideally within the same transaction as the ALTER TABLE)
-- Use IF EXISTS to prevent errors if the index doesn't exist or was already dropped.
DROP INDEX IF EXISTS seller.idx_seller_info_username_lower;

-- 3. Alter the column type to citext (this might take time and lock the table on large datasets)
ALTER TABLE seller.seller_info ALTER COLUMN username TYPE citext;


-- 4. Create a new standard B-tree index on the citext column
-- Use IF NOT EXISTS to prevent errors if an index with this name already exists.
CREATE INDEX IF NOT EXISTS idx_seller_info_username ON seller.seller_info (username);

-- Commit the transaction if all steps were successful
COMMIT;
-- Or use ROLLBACK; if any step failed to undo the changes within the transaction.





DROP INDEX seller.idx_seller_info_username_lower;

SELECT indexname, indexdef FROM pg_indexes WHERE schemaname = 'seller' AND tablename = 'seller_info';
