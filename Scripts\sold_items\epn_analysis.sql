
--<PERSON>, missing in epn, present in sold_items
SELECT si.*, et.customid  AS "customid"
FROM "sold_items" si
LEFT JOIN epn_transactions et  ON si."item_id" =et.itemid 
where et.customid like '%931A-721A%' 
and listing_status =2
--and si.buyer_name = 'l_' 
--AND (si.buyer_feedback_count >8400 and si.buyer_feedback_count <8500  or si.buyer_feedback_count =0)
--AND si."leaf_category_id" IN (124603, 42894, 78192, 181717, 181714, 78191, 57520, 181709, 181708)
--and si.leaf_category_id =16204
order by end_time desc



--for each epn item find sold_item match 
SELECT
    et.*,
    si.leaf_category_id,
    si.start_time,
    si.end_time,
    si.price,
    si.quantity_sold,
    si.duration,
    si.buyer_name,
    si.buyer_feedback_count,
    si.listing_status,
    si.fetch_time   
FROM
    public.epn_transactions et 
LEFT JOIN public.sold_items si ON et.itemid = si.item_id
where et.eventdate >'2025-05-01' and et.customid like '%931A-721A%' --and si.buyer_name is null--and et.leafcategoryid =181708
ORDER by et.eventdate desc;

--fast purchases with/without our customid
select
    et.customid,
    si.leaf_category_id,
    si.start_time,
    si.end_time,
    si.price,
    si.quantity_sold,
    si.duration,
    --si.high_bidder,
    si.listing_status,
    si.fetch_time
from
    public.sold_items si
left join public.epn_transactions et on
    et.itemid = si.item_id
where
    si.leaf_category_id = 16204
order by
    si.end_time desc ;

--items missing in sold_items
SELECT (et.itemid) AS missing_item_count
FROM public.epn_transactions et 
LEFT JOIN public.sold_items si ON et.itemid = si.item_id
WHERE si.item_id IS NULL
AND et.eventdate > '2024-02-06';

--items from epn is missing in sold items based on itemid in category
SELECT e.itemid, e.customid, s.buyer_name, s.duration, s.listing_status, s.price, s.start_time, s.leaf_category_id 
FROM public.epn_transactions e
LEFT JOIN public.sold_items s ON e.itemid = s.item_id AND e.leafcategoryid = s.leaf_category_id
WHERE e.leafcategoryid = 111422 and s.listing_status=2;




--Peter, missing in epn, present in sold_items
SELECT 
   *
FROM 
    public.sold_items  si
WHERE 
buyer_name = 'is' 
    AND (si.buyer_feedback_count >8400 and si.buyer_feedback_count <8500  or si.buyer_feedback_count =0)
    AND "leaf_category_id" IN (124603, 42894, 78192, 181717, 181714, 78191, 57520, 181709, 181708)
order by end_time desc

--missing in epn, present in sold
SELECT 
    si.*, 
    et.*
FROM 
    "sold_items" si
LEFT JOIN 
    epn_transactions et 
ON 
    si."item_id" = et.itemid
WHERE 
    si.buyer_name = 'is'
--    AND si.listing_status = 2
    AND (si.buyer_feedback_count BETWEEN 8401 AND 8499 OR si.buyer_feedback_count = 0)
    AND si."leaf_category_id" IN (124603, 42894, 78192, 181717, 181714, 78191, 57520, 181709, 181708)
--    and si.leaf_category_id =16204
ORDER BY 
    si.end_time DESC;





--missing in epn, present in sold
select
	si.*,
	et.*
from
	"sold_items" si
left join 
    epn_transactions et 
on
	si."item_id" = et.itemid
where
	((si.buyer_name = 'hs'
		and si.buyer_feedback_count >27000
		and si.buyer_feedback_count <31000)
	or (si.buyer_name = 'bn'
		and si.buyer_feedback_count >16000
		and si.buyer_feedback_count <18000)
	or (si.buyer_name = 'ss'
		and si.buyer_feedback_count = 0)
    )
	and 
	si.listing_status = 2
	and si.leaf_category_id = 16204
	and fetch_time > '2024-12-01'
order by
	si.end_time desc;



--missing in epn, present in sold
SELECT 
    si.*, 
    et.*
FROM 
    "sold_items" si
WHERE 
    si.buyer_name = 'hs'
--    AND si.listing_status = 2
--    AND (si.buyer_feedback_count BETWEEN 8401 AND 8499 OR si.buyer_feedback_count = 0)
--    AND si."leaf_category_id" IN (124603, 42894, 78192, 181717, 181714, 78191, 57520, 181709, 181708)
    and .leafcategoryid =16204
ORDER BY 
    si.end_time DESC;




ot - tonerone
r_ - frei
-0 frei
re The Jolly Savage Ltd

--group by buyer name in category
SELECT 
    si.buyer_name, 
    COUNT(*) AS count,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY si.duration) AS median_duration
FROM 
    public.sold_items si
WHERE 
    si.leaf_category_id = 16204
    AND si.end_time > '2024-12-01'
    AND si.duration < 1200
GROUP BY 
    si.buyer_name
ORDER BY 
    count DESC;
