SELECT * from commit_to_buy_log ctbl 
WHERE ctbl.item_id =226384711315


SELECT * from commit_to_buy_log ctbl 
WHERE ctbl.license_key ='0E73-5C3A-D8F2-4058-BB92-ED88-92A2'

SELECT * from commit_to_buy_log ctbl 
WHERE ctbl.token ='v^1.1#i^1#I^3#r^1#p^3#f^0#t^Ul4xMF84OkUzRUY0RDAwNTA4MjRFMjg2MUM5MTRFMjMwQjA5RTQ1XzBfMSNFXjI2MA=='

#--sum of paid/non paid 

SELECT currency_id ,buyer_paid_status,
       round(SUM(price * quantity)) AS total_value
FROM commit_to_buy_log
GROUP BY currency_id ,buyer_paid_status;

SELECT license_key,
       buyer_paid_status,
       ROUND( SUM(price * quantity)) AS total_value
FROM commit_to_buy_log
GROUP BY license_key, buyer_paid_status;

 

SELECT
	DISTINCT (username)
FROM
	commit_to_buy_log
WHERE
	transaction_time >= '2024-10-08'
	AND transaction_time < '2024-10-16'
	AND license_key = 'DD35-5875-921B-4EAB-AB23-0FB8-9890'
	
--replace short keys with long keys	

UPDATE
	commit_to_buy_log AS short_log
JOIN commit_to_buy_log AS long_log
ON
	short_log.license_key = LEFT(long_log.license_key,
	9)
	AND LENGTH(long_log.license_key) > 9
SET
	short_log.license_key = long_log.license_key
WHERE
	LENGTH(short_log.license_key) = 9;
	
	
 --preview short to long convertion	
 
SELECT short_log.license_key AS old_license_key, 
       long_log.license_key AS new_license_key,
       short_log.transaction_id,
       short_log.item_id
FROM commit_to_buy_log AS short_log
JOIN commit_to_buy_log AS long_log
ON short_log.license_key = LEFT(long_log.license_key, 9)
AND LENGTH(long_log.license_key) > 9
WHERE LENGTH(short_log.license_key) = 9;

 --Doesnt have long key
SELECT short_log.license_key AS short_license_key,
       short_log.transaction_id,
       short_log.item_id
FROM commit_to_buy_log AS short_log
LEFT JOIN commit_to_buy_log AS long_log
ON short_log.license_key = LEFT(long_log.license_key, 9)
AND LENGTH(long_log.license_key) > 9
WHERE LENGTH(short_log.license_key) = 9
AND long_log.license_key IS NULL;



# Paid/Canceled by license key
SELECT
    license_key,
    SUM(CASE
        WHEN buyer_paid_status IS NOT NULL AND buyer_paid_status != 'NotPaid'
        AND (cancel_status IS NULL OR cancel_status = '')
        THEN price * quantity
        ELSE 0
    END) AS Paid,
    SUM(CASE
        WHEN NOT (buyer_paid_status IS NOT NULL AND buyer_paid_status != 'NotPaid'
        AND (cancel_status IS NULL OR cancel_status = ''))
        THEN price * quantity
        ELSE 0
    END) AS Canceled,
        COUNT(CASE
        WHEN buyer_paid_status IS NOT NULL AND buyer_paid_status != 'NotPaid'
        AND (cancel_status IS NULL OR cancel_status = '')
        THEN 1
        ELSE NULL
    END) AS Paid_Count,
    COUNT(CASE
        WHEN NOT (buyer_paid_status IS NOT NULL AND buyer_paid_status != 'NotPaid'
        AND (cancel_status IS NULL OR cancel_status = ''))
        THEN 1
        ELSE NULL
    END) AS Canceled_Count
FROM
    commit_to_buy_log
GROUP BY
    license_key;
   
   
#get placeoffer data
   
select 
transaction_time,
item_id,
transaction_id,
price,
quantity,
hwid,
license_key,
username,
buyer_paid_status,
payment_hold_status,
cancel_status
from commit_to_buy_log



CREATE TABLE `commit_to_buy_eias_token` (
  `updated_time` datetime NOT NULL,
  `ip` varchar(45) NOT NULL,  
  `eias` varchar(255) DEFAULT NULL,
  `token` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `commit_count` int DEFAULT NULL,  
  `hwid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `license_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;	



  UPDATE commit_to_buy_eias_token 
                SET updated_time = '2025-05-05 12:45:18', ip = '***********', username = 'decoderworld', hwid = 'gHwB7NLknXuE2/P9oiCgLcIo+r4=', license_key = 'ROMA-1D94-5823-458F-96DB-CED1-B99F', commit_count = commit_count + 1
                WHERE license_key like 'ROMA-1D94-5823-458F-96DB-CED1-B99F%';
