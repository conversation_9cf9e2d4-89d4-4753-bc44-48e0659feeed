--Daily transaction count analysis
SELECT
    DATE(end_time) AS end_time,
    COUNT(*) AS total_rows
FROM
    public.sold_items
WHERE
   end_time >= '2025-03-01 08:00:00' 
    and     end_time < '2025-05-15 08:00:00' 
    --and leaf_category_id =9355
--and buyer_feedback_count = 0 and buyer_name <>''
GROUP BY
    DATE(end_time)
ORDER BY
    end_time;

--Daily transaction count analysis
SELECT
    DATE(start_time) AS start_time,
    COUNT(*) AS total_rows
FROM
    public.sold_items
WHERE
   start_time >= '2025-03-01 08:00:00' 
    and     start_time < '2025-03-15 08:00:00' 
    --and leaf_category_id =9355
--and buyer_feedback_count = 0 and buyer_name <>''
GROUP BY
    DATE(start_time)
ORDER BY
    start_time;


--Count transactions by specific dates
SELECT 
    COUNT(CASE WHEN "end_time"::date = '2025-01-18' THEN 1 END) AS count_2025_01_18,
    COUNT(CASE WHEN "end_time"::date = '2024-12-29' THEN 1 END) AS count_2024_12_29
FROM 
    "sold_items";

--Transactions by day with additional metrics
SELECT 
    DATE_TRUNC('day', end_time) AS day, 
    COUNT(*) as transactions
FROM 
    sold_items si
WHERE
    end_time > '2024-09-26'
GROUP BY
    DATE_TRUNC('day', end_time)
ORDER BY
    day;
