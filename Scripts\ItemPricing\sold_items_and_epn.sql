--count by day

 select
	<PERSON><PERSON><PERSON>(fetch_time) as fetch_date,
	COUNT(fetch_time) as total_rows,
	count(listing_status) as listing_status
from
	public.sold_items
where
	--fetch_time >= '2024-02-05' --and leaf_category_id=100982	
	fetch_time >= '2024-12-29 08:00:00'
    and leaf_category_id=9355
	--and listing_status=0 
group by
	DATE(fetch_time)
order by
	fetch_date;
  ------------------------
  select
	D<PERSON><PERSON>(end_time ) as end_time,
	COUNT(end_time) as total_rows,
	count(listing_status) as listing_status
from
	public.sold_items
where
	--fetch_time >= '2024-02-05' --and leaf_category_id=100982	
	end_time >= '2024-12-20 08:00:00' and end_time < '2025-02-06 08:00:00'
--	and leaf_category_id in (260260,181709,260261,181710,181711,181713)
	--and leaf_category_id in (9355)
--    and leaf_category_id in (42892,78186,78188,260264,42894,78190,78193,78191,71393,71394,78192,181698,181699,181700,260265,181707,181701,181706,181702,181704,181705,260266,181730,260267,181731,181734,65452,181732,260268,260269,124603,260270,9723,181733,260271,181793,181807,181802,181803,181804,181808,73129,181806,260272,50924,181748,181749,260273,181750,260275,260278,260279,185120,260276,260277,181751,181752,181753,260274,181754,260280,181760,181767,181759,181766,260281,260282,260283,260284,260285,181763,181762,181772,181773,181774,260286,181775,181776,260287,185121,260288,181777,181778,260289,181768,181770,185122,181769,181771,181783,260290,181756,181779,181757,181780,181781,181782,260291,260292,181764,260293,260294,260295,36802,57516,260260,181709,260261,181710,181711,181713,181715,260262,181725,181716,260263,181717,181714,181718,181719,181720,181721,181722,181723,181724,181708,181712,181736,181737,181739,181740,55826,181741,181742,260296,260297,260298,181747,260299,181743,181729,260300,183548,181746,181784,181785,181786,181787,65458,57520,181788,65456,65459,65455,181790,65460,181791,181792)
	--and listing_status=0 
group by
	DATE(end_time)
order by
	end_time;
 
 
 SELECT "leaf_category_id", COUNT(*) AS category_count 
FROM "sold_items" 
WHERE "fetch_time"::date = '2025-01-18' 
GROUP BY "leaf_category_id" 
ORDER BY category_count DESC 
--LIMIT 50;

 SELECT 
    "leaf_category_id", 
    COUNT(CASE WHEN "end_time"::date = '2025-01-18' THEN 1 END) AS count_2025_01_18,
    COUNT(CASE WHEN "end_time"::date = '2024-12-29' THEN 1 END) AS count_2024_12_29
FROM 
    "sold_items"
WHERE 
    "fetch_time"::date IN ('2025-01-18', '2024-12-29')
GROUP BY 
    "leaf_category_id"
ORDER BY 
    "leaf_category_id";
 --
 
SELECT item_id 
FROM public.sold_items
WHERE (listing_status = 2 AND end_time > '2024-11-23') 
   --OR (listing_status = 0)
 
 
 
 
  --count by day EPN
 select
	DATE(clicktimestamp) as clicktimestamp ,
	COUNT(*) as total_rows
from
	public.epn_transactions et
where
	clicktimestamp >= '2024-01-01'
group by
	DATE(clicktimestamp)
order by
	clicktimestamp ;
  

    


select si.item_id  from sold_items si 
where buyer_name = '^^'

