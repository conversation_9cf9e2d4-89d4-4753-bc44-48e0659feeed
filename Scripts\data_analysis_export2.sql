--get missing sellers info in data analysis tables
SELECT DISTINCT r.seller_name AS username
FROM data_analysis."_2025_02_26_fragrances"  r  
LEFT JOIN seller.seller_info s
ON r.seller_name = s.username
WHERE s.username IS NULL;





-- Get the current sequence value
SELECT currval('seller.seller_info_seller_id_seq') AS current_sequence_value,
       nextval('seller.seller_info_seller_id_seq') AS next_sequence_value,
       MAX(user_id) AS max_user_id
FROM seller.seller_info;

SELECT nextval('seller.seller_info_seller_id_seq') AS next_sequence_value,
       MAX(user_id) AS max_user_id
FROM seller.seller_info;

SELECT setval('seller.seller_info_seller_id_seq', 13182325 + 1);


-- Check the maximum user_id in the table
SELECT MAX(user_id) AS max_user_id FROM seller.seller_info;






WITH country_mapping AS (
    SELECT 4 AS country_id, 'Afghanistan' AS country_name
    UNION ALL SELECT 8, 'Albania'
    UNION ALL SELECT 12, 'Algeria'
    UNION ALL SELECT 16, 'American Samoa'
    UNION ALL SELECT 20, 'Andorra'
    UNION ALL SELECT 24, 'Angola'
    UNION ALL SELECT 660, 'Anguilla'
    UNION ALL SELECT 28, 'Antigua and Barbuda'
    UNION ALL SELECT 32, 'Argentina'
    UNION ALL SELECT 51, 'Armenia'
    UNION ALL SELECT 533, 'Aruba'
    UNION ALL SELECT 36, 'Australia'
    UNION ALL SELECT 40, 'Austria'
    UNION ALL SELECT 31, 'Republic of Azerbaijan'
    UNION ALL SELECT 44, 'Bahamas'
    UNION ALL SELECT 48, 'Bahrain'
    UNION ALL SELECT 50, 'Bangladesh'
    UNION ALL SELECT 52, 'Barbados'
    UNION ALL SELECT 112, 'Belarus'
    UNION ALL SELECT 56, 'Belgium'
    UNION ALL SELECT 84, 'Belize'
    UNION ALL SELECT 204, 'Benin'
    UNION ALL SELECT 60, 'Bermuda'
    UNION ALL SELECT 64, 'Bhutan'
    UNION ALL SELECT 68, 'Bolivia'
    UNION ALL SELECT 70, 'Bosnia and Herzegovina'
    UNION ALL SELECT 72, 'Botswana'
    UNION ALL SELECT 76, 'Brazil'
    UNION ALL SELECT 92, 'British Virgin Islands'
    UNION ALL SELECT 96, 'Brunei Darussalam'
    UNION ALL SELECT 100, 'Bulgaria'
    UNION ALL SELECT 854, 'Burkina Faso'
    UNION ALL SELECT 108, 'Burundi'
    UNION ALL SELECT 116, 'Cambodia'
    UNION ALL SELECT 120, 'Cameroon'
    UNION ALL SELECT 124, 'Canada'
    UNION ALL SELECT 132, 'Cape Verde Islands'
    UNION ALL SELECT 136, 'Cayman Islands'
    UNION ALL SELECT 140, 'Central African Republic'
    UNION ALL SELECT 148, 'Chad'
    UNION ALL SELECT 152, 'Chile'
    UNION ALL SELECT 156, 'China'
    UNION ALL SELECT 170, 'Colombia'
    UNION ALL SELECT 174, 'Comoros'
    UNION ALL SELECT 180, 'Congo, Democratic Republic of the'
    UNION ALL SELECT 178, 'Congo, Republic of the'
    UNION ALL SELECT 184, 'Cook Islands'
    UNION ALL SELECT 188, 'Costa Rica'
    UNION ALL SELECT 384, 'Cote d Ivoire (Ivory Coast)'
    UNION ALL SELECT 191, 'Republic of Croatia'
    UNION ALL SELECT 531, 'Curacao'
    UNION ALL SELECT 196, 'Cyprus'
    UNION ALL SELECT 203, 'Czech Republic'
    UNION ALL SELECT 208, 'Denmark'
    UNION ALL SELECT 262, 'Djibouti'
    UNION ALL SELECT 212, 'Dominica'
    UNION ALL SELECT 214, 'Dominican Republic'
    UNION ALL SELECT 218, 'Ecuador'
    UNION ALL SELECT 818, 'Egypt'
    UNION ALL SELECT 222, 'El Salvador'
    UNION ALL SELECT 226, 'Equatorial Guinea'
    UNION ALL SELECT 232, 'Eritrea'
    UNION ALL SELECT 233, 'Estonia'
    UNION ALL SELECT 231, 'Ethiopia'
    UNION ALL SELECT 238, 'Falkland Islands (Islas Malvinas)'
    UNION ALL SELECT 242, 'Fiji'
    UNION ALL SELECT 246, 'Finland'
    UNION ALL SELECT 250, 'France'
    UNION ALL SELECT 254, 'French Guiana'
    UNION ALL SELECT 258, 'French Polynesia'
    UNION ALL SELECT 266, 'Gabon Republic'
    UNION ALL SELECT 270, 'Gambia'
    UNION ALL SELECT 268, 'Georgia'
    UNION ALL SELECT 276, 'Germany'
    UNION ALL SELECT 288, 'Ghana'
    UNION ALL SELECT 292, 'Gibraltar'
    UNION ALL SELECT 300, 'Greece'
    UNION ALL SELECT 304, 'Greenland'
    UNION ALL SELECT 308, 'Grenada'
    UNION ALL SELECT 312, 'Guadeloupe'
    UNION ALL SELECT 316, 'Guam'
    UNION ALL SELECT 320, 'Guatemala'
    UNION ALL SELECT 831, 'Guernsey'
    UNION ALL SELECT 324, 'Guinea'
    UNION ALL SELECT 624, 'Guinea-Bissau'
    UNION ALL SELECT 328, 'Guyana'
    UNION ALL SELECT 332, 'Haiti'
    UNION ALL SELECT 340, 'Honduras'
    UNION ALL SELECT 344, 'Hong Kong'
    UNION ALL SELECT 348, 'Hungary'
    UNION ALL SELECT 352, 'Iceland'
    UNION ALL SELECT 356, 'India'
    UNION ALL SELECT 360, 'Indonesia'
    UNION ALL SELECT 372, 'Ireland'
    UNION ALL SELECT 376, 'Israel'
    UNION ALL SELECT 380, 'Italy'
    UNION ALL SELECT 388, 'Jamaica'
    UNION ALL SELECT 744, 'Jan Mayen'
    UNION ALL SELECT 392, 'Japan'
    UNION ALL SELECT 832, 'Jersey'
    UNION ALL SELECT 400, 'Jordan'
    UNION ALL SELECT 398, 'Kazakhstan'
    UNION ALL SELECT 404, 'Kenya'
    UNION ALL SELECT 296, 'Kiribati'
    UNION ALL SELECT 410, 'South Korea'
    UNION ALL SELECT 414, 'Kuwait'
    UNION ALL SELECT 417, 'Kyrgyzstan'
    UNION ALL SELECT 418, 'Laos'
    UNION ALL SELECT 428, 'Latvia'
    UNION ALL SELECT 422, 'Lebanon'
    UNION ALL SELECT 438, 'Liechtenstein'
    UNION ALL SELECT 440, 'Lithuania'
    UNION ALL SELECT 442, 'Luxembourg'
    UNION ALL SELECT 446, 'Macau'
    UNION ALL SELECT 807, 'Macedonia'
    UNION ALL SELECT 450, 'Madagascar'
    UNION ALL SELECT 454, 'Malawi'
    UNION ALL SELECT 458, 'Malaysia'
    UNION ALL SELECT 462, 'Maldives'
    UNION ALL SELECT 466, 'Mali'
    UNION ALL SELECT 470, 'Malta'
    UNION ALL SELECT 584, 'Marshall Islands'
    UNION ALL SELECT 474, 'Martinique'
    UNION ALL SELECT 478, 'Mauritania'
    UNION ALL SELECT 480, 'Mauritius'
    UNION ALL SELECT 175, 'Mayotte'
    UNION ALL SELECT 484, 'Mexico'
    UNION ALL SELECT 583, 'Micronesia'
    UNION ALL SELECT 498, 'Moldova'
    UNION ALL SELECT 492, 'Monaco'
    UNION ALL SELECT 496, 'Mongolia'
    UNION ALL SELECT 499, 'Montenegro'
    UNION ALL SELECT 500, 'Montserrat'
    UNION ALL SELECT 504, 'Morocco'
    UNION ALL SELECT 508, 'Mozambique'
    UNION ALL SELECT 516, 'Namibia'
    UNION ALL SELECT 520, 'Nauru'
    UNION ALL SELECT 524, 'Nepal'
    UNION ALL SELECT 528, 'Netherlands'
    UNION ALL SELECT 530, 'Netherlands Antilles'
    UNION ALL SELECT 540, 'New Caledonia'
    UNION ALL SELECT 554, 'New Zealand'
    UNION ALL SELECT 558, 'Nicaragua'
    UNION ALL SELECT 562, 'Niger'
    UNION ALL SELECT 566, 'Nigeria'
    UNION ALL SELECT 570, 'Niue'
    UNION ALL SELECT 578, 'Norway'
    UNION ALL SELECT 512, 'Oman'
    UNION ALL SELECT 586, 'Pakistan'
    UNION ALL SELECT 585, 'Palau'
    UNION ALL SELECT 591, 'Panama'
    UNION ALL SELECT 598, 'Papua New Guinea'
    UNION ALL SELECT 600, 'Paraguay'
    UNION ALL SELECT 604, 'Peru'
    UNION ALL SELECT 608, 'Philippines'
    UNION ALL SELECT 616, 'Poland'
    UNION ALL SELECT 620, 'Portugal'
    UNION ALL SELECT 630, 'Puerto Rico'
    UNION ALL SELECT 634, 'Qatar'
    UNION ALL SELECT 638, 'Reunion'
    UNION ALL SELECT 642, 'Romania'
    UNION ALL SELECT 643, 'Russian Federation'
    UNION ALL SELECT 646, 'Rwanda'
    UNION ALL SELECT 654, 'Saint Helena'
    UNION ALL SELECT 659, 'Saint Kitts-Nevis'
    UNION ALL SELECT 662, 'Saint Lucia'
    UNION ALL SELECT 666, 'Saint Pierre and Miquelon'
    UNION ALL SELECT 670, 'Saint Vincent and the Grenadines'
    UNION ALL SELECT 674, 'San Marino'
    UNION ALL SELECT 682, 'Saudi Arabia'
    UNION ALL SELECT 686, 'Senegal'
    UNION ALL SELECT 688, 'Serbia'
    UNION ALL SELECT 690, 'Seychelles'
    UNION ALL SELECT 694, 'Sierra Leone'
    UNION ALL SELECT 702, 'Singapore'
    UNION ALL SELECT 703, 'Slovakia'
    UNION ALL SELECT 705, 'Slovenia'
    UNION ALL SELECT 90, 'Solomon Islands'
    UNION ALL SELECT 706, 'Somalia'
    UNION ALL SELECT 710, 'South Africa'
    UNION ALL SELECT 724, 'Spain'
    UNION ALL SELECT 144, 'Sri Lanka'
    UNION ALL SELECT 740, 'Suriname'
    UNION ALL SELECT 744, 'Svalbard'
    UNION ALL SELECT 748, 'Swaziland'
    UNION ALL SELECT 752, 'Sweden'
    UNION ALL SELECT 756, 'Switzerland'
    UNION ALL SELECT 258, 'Tahiti'
    UNION ALL SELECT 158, 'Taiwan'
    UNION ALL SELECT 762, 'Tajikistan'
    UNION ALL SELECT 834, 'Tanzania'
    UNION ALL SELECT 764, 'Thailand'
    UNION ALL SELECT 768, 'Togo'
    UNION ALL SELECT 776, 'Tonga'
    UNION ALL SELECT 780, 'Trinidad and Tobago'
    UNION ALL SELECT 788, 'Tunisia'
    UNION ALL SELECT 792, 'Turkey'
    UNION ALL SELECT 795, 'Turkmenistan'
    UNION ALL SELECT 796, 'Turks and Caicos Islands'
    UNION ALL SELECT 798, 'Tuvalu'
    UNION ALL SELECT 800, 'Uganda'
    UNION ALL SELECT 804, 'Ukraine'
    UNION ALL SELECT 784, 'United Arab Emirates'
    UNION ALL SELECT 826, 'United Kingdom'
    UNION ALL SELECT 840, 'United States'
    UNION ALL SELECT 858, 'Uruguay'
    UNION ALL SELECT 860, 'Uzbekistan'
    UNION ALL SELECT 548, 'Vanuatu'
    UNION ALL SELECT 336, 'Vatican City'
    UNION ALL SELECT 862, 'Venezuela'
    UNION ALL SELECT 704, 'Vietnam'
    UNION ALL SELECT 850, 'Virgin Islands'
    UNION ALL SELECT 876, 'Wallis and Futuna'
    UNION ALL SELECT 732, 'Western Sahara'
    UNION ALL SELECT 882, 'Western Samoa'
    UNION ALL SELECT 887, 'Yemen'
    UNION ALL SELECT 894, 'Zambia'
    UNION ALL SELECT 716, 'Zimbabwe'
),
seller_info_with_country_names AS (
    SELECT
        s.*,
        cm2.country_name AS seller_user_web_country_name,
        cm3.country_name AS seller_user_api_country_name,
        cm4.country_name AS seller_store_web_country_name,
        cm5.country_name AS seller_store_api_country_name
    FROM
        seller.seller_info AS s      
    LEFT JOIN country_mapping cm2 ON s.user_web_country_id::int = cm2.country_id
    LEFT JOIN country_mapping cm3 ON s.user_api_country_id::int = cm3.country_id
    LEFT JOIN country_mapping cm4 ON s.store_web_country_id::int = cm4.country_id
    LEFT JOIN country_mapping cm5 ON s.store_api_country_id::int = cm5.country_id
)
SELECT
    i.search_name,
    i.item_id,
    i.category_id,
    i.title,
    i.price,
    i.shipping_price,
    i.shipping_type,
    i.shipping_cost_paid_by_option,
    i.dispatch_time_max,
    i.country_from,
    s.seller_user_web_country_name,
    s.seller_user_api_country_name,
    s.seller_store_web_country_name,
    s.seller_store_api_country_name,
    i.postal_code,
    i.condition_id,
    CASE 
        WHEN i.condition_id = 1000 THEN 'New'
        WHEN i.condition_id = 1500 THEN 'New other'
        WHEN i.condition_id = 1750 THEN 'New with defects'
        WHEN i.condition_id = 2000 THEN 'Manufacturer refurbished'
        WHEN i.condition_id = 2010 THEN 'Excellent Refurbished'
        WHEN i.condition_id = 2020 THEN 'Very Good Refurbished'
        WHEN i.condition_id = 2030 THEN 'Good Refurbished'
        WHEN i.condition_id = 2500 THEN 'Seller refurbished'
        WHEN i.condition_id = 2750 THEN 'Like new'
        WHEN i.condition_id = 3000 THEN 'Used'
        WHEN i.condition_id = 4000 THEN 'Very good'
        WHEN i.condition_id = 5000 THEN 'Good'
        WHEN i.condition_id = 6000 THEN 'Acceptable'
        WHEN i.condition_id = 7000 THEN 'For parts or not working'
        ELSE 'Unspecified'
    END AS condition_name,
    i.duration_seconds,
    i.sub_title,
    i.condition_description,
    i.description,
    i.specifics,
    i.brand,
    i.model,
    i.mpn,
    i.upc,
    i.product_reference_id,
    i.seller_name,
    i.seller_feedback_count,
    i.seller_feedback_percent,    
    s.store_name AS seller_store_name,
    s.registration_date AS seller_registration_date,
    s.transaction_percent AS seller_transaction_percent,
    s.store_owner AS seller_store_owner,
    s.business AS seller_business,
    s.top_rated AS seller_top_rated,
    s.feedback_private AS seller_feedback_private,
    s.verified AS seller_verified,    
    i.buyer_name,
    i.buyer_feedback_count,
    i.buyer_feedback_percent,
    i.buyer_feedback_private,
    i.buyer_protection,
    i.quantity_total,
    i.quantity_sold,
    i.lot_size,
    i.bid_count,
    i.picture_count,
    i.best_offer,
    i.get_it_fast,
    i.relist_parent_id,
    i.refund_option,
    i.returns_accepted_option,
    i.returns_within_option,
    i.revised,
    i.top_rated_listing,
    i.sold_as_bin,
    i.bin_sold,
    i.auction_sold,
    i.bin_not_sold,
    i.auction_not_sold,
    i.best_offer_sold,
    i.commit_to_buy,
    i.listing_type,
    i.pictures_url,
    i.time_posted,
    i.time_ended,
    i.updated_at
FROM 
    data_analysis."_2025_03_21_9355"     AS i    
LEFT JOIN 
    seller_info_with_country_names AS s ON i.seller_name = s.username
--WHERE 
    --i.duration_seconds <= 900;
