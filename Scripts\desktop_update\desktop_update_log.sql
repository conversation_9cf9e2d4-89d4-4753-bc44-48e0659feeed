--count online days per license key
    
    SELECT 
    license_key,
    COUNT(DISTINCT DATE(check_time)) AS days_online
FROM 
    desktop_app.desktop_update_log
WHERE 
    check_time IS NOT null and check_time > '2024-01-01'
GROUP BY 
    license_key
    order by days_online desc;
    
--licensekey and unique check date   
SELECT 
    license_key,
    DATE(check_time) AS check_date
FROM 
    desktop_app.desktop_update_log
WHERE 
    check_time IS NOT NULL 
    AND TRIM(license_key) <> ''
    --AND check_time > '2024-09-01'
GROUP BY 
    license_key, DATE(check_time)
ORDER BY 
    license_key, check_date;

