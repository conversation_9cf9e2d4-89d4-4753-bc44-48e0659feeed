{"folders": {}, "connections": {"postgres-jdbc-1848fe8a884-2570dcd47814887a": {"provider": "postgresql", "driver": "postgres-jdbc", "name": "ubf1", "save-password": true, "configuration": {"host": "reactnative-apps.ckjfvcdemxab.us-west-2.rds.amazonaws.com", "port": "5432", "url": "********************************************************************************/", "configurationType": "MANUAL", "home": "postgresql_client", "type": "dev", "closeIdleConnection": false, "config-profile": "push.ubuyfirst.net", "properties": {"connectTimeout": "20", "loginTimeout": "20"}, "provider-properties": {"@dbeaver-show-non-default-db@": "true", "@dbeaver-show-template-db@": "false", "@dbeaver-show-unavailable-db@": "false", "show-database-statistics": "true", "@dbeaver-read-all-data-types-db@": "true", "read-keys-with-columns": "true", "@dbeaver-use-prepared-statements-db@": "false", "postgresql.dd.plain.string": "false", "postgresql.dd.tag.string": "false", "@dbeaver-chosen-role@": ""}, "auth-model": "native", "handlers": {"ssh_tunnel": {"type": "TUNNEL", "enabled": true}}}}, "postgres-jdbc-18d2d848c32-2de084c5d26b2993": {"provider": "postgresql", "driver": "postgres-jdbc", "name": "seer1 - sold_items", "save-password": true, "configuration": {"host": "ubf.dynu.net", "port": "5432", "database": "item_speed_db", "url": "*************************************************", "configurationType": "MANUAL", "home": "postgresql_client", "type": "dev", "closeIdleConnection": false, "auth-model": "native"}}, "postgres-jdbc-1914596aa90-29f70c8676716b7": {"provider": "postgresql", "driver": "postgres-jdbc", "name": "ubuyfirst-reports", "save-password": true, "configuration": {"host": "ubuyfirst-reports.ckjfvcdemxab.us-west-2.rds.amazonaws.com", "port": "5432", "database": "postgres", "url": "******************************************************************************************", "configurationType": "MANUAL", "home": "postgresql_client", "type": "dev", "closeIdleConnection": true, "provider-properties": {"@dbeaver-show-non-default-db@": "false", "@dbeaver-chosen-role@": ""}, "auth-model": "native"}}, "postgres-jdbc-1914fae26d1-7ede4a24713056f8": {"provider": "postgresql", "driver": "postgres-jdbc", "name": "placeoffer", "save-password": true, "configuration": {"host": "ubuyfirst-reports.ckjfvcdemxab.us-west-2.rds.amazonaws.com", "port": "5432", "database": "postgres", "url": "******************************************************************************************", "configurationType": "MANUAL", "home": "postgresql_client", "type": "dev", "closeIdleConnection": true, "provider-properties": {"@dbeaver-show-non-default-db@": "false", "@dbeaver-chosen-role@": ""}, "auth-model": "native"}}, "postgres-jdbc-1917629ff22-a4d01436bbd82e": {"provider": "postgresql", "driver": "postgres-jdbc", "name": "placeoffer-postgres", "save-password": true, "configuration": {"host": "ubuyfirst-reports.ckjfvcdemxab.us-west-2.rds.amazonaws.com", "port": "5432", "database": "postgres", "url": "******************************************************************************************", "configurationType": "MANUAL", "home": "postgresql_client", "type": "dev", "closeIdleConnection": true, "properties": {"Timezone": "UTC", "connectTimeout": "20", "loginTimeout": "20", "escapeSyntaxCallMode": "callIfNoReturn"}, "provider-properties": {"@dbeaver-show-non-default-db@": "false", "@dbeaver-chosen-role@": "", "@dbeaver-show-template-db@": "false", "@dbeaver-show-unavailable-db@": "false", "show-database-statistics": "false", "@dbeaver-read-all-data-types-db@": "false", "read-keys-with-columns": "false", "@dbeaver-use-prepared-statements-db@": "false", "postgresql.dd.plain.string": "false", "postgresql.dd.tag.string": "false"}, "auth-model": "native"}, "extensions": {"dashboards": "<dashboardList><dashboards id=\"default\" name=\"Default\"><view/><dashboard id=\"database:postgresql.sessionCount\" viewType=\"timeseries\" index=\"0\" widthRatio=\"1.5\" updatePeriod=\"30000\" maxItems=\"300\" maxAge=\"1800000\" legendVisible=\"true\" gridVisible=\"true\" domainTicksVisible=\"true\" rangeTicksVisible=\"true\" description=\"Shows active/idle server sessions\"/><dashboard id=\"database:postgresql.transactionCount\" viewType=\"timeseries\" index=\"1\" widthRatio=\"1.5\" updatePeriod=\"1000\" maxItems=\"300\" maxAge=\"1800000\" legendVisible=\"true\" gridVisible=\"true\" domainTicksVisible=\"true\" rangeTicksVisible=\"true\" description=\"Shows commit/rollback transactions per second\"/><dashboard id=\"database:postgresql.blockIO\" viewType=\"timeseries\" index=\"2\" widthRatio=\"1.5\" updatePeriod=\"30000\" maxItems=\"300\" maxAge=\"1800000\" legendVisible=\"true\" gridVisible=\"true\" domainTicksVisible=\"true\" rangeTicksVisible=\"true\" description=\"Shows blocking IO operations per second\"/></dashboards></dashboardList>"}, "custom-properties": {"resultset.confirm.beforeSave": "false", "resultset.datetime.editor": "false"}}, "postgres-jdbc-19282a6c266-506942572d949dd": {"provider": "postgresql", "driver": "postgres-jdbc", "name": "1", "save-password": true, "configuration": {"host": "ubuyfirst-item-data-instance-1.ckjfvcdemxab.us-west-2.rds.amazonaws.com", "port": "5432", "database": "postgres", "url": "*******************************************************************************************************", "configurationType": "MANUAL", "home": "postgresql_client", "type": "dev", "closeIdleConnection": true, "properties": {"connectTimeout": "20", "loginTimeout": "20", "escapeSyntaxCallMode": "callIfNoReturn"}, "provider-properties": {"@dbeaver-show-non-default-db@": "false", "@dbeaver-chosen-role@": "", "@dbeaver-show-template-db@": "false", "@dbeaver-show-unavailable-db@": "false", "show-database-statistics": "false", "@dbeaver-read-all-data-types-db@": "false", "read-keys-with-columns": "false", "@dbeaver-use-prepared-statements-db@": "false", "postgresql.dd.plain.string": "false", "postgresql.dd.tag.string": "false"}, "auth-model": "native"}}, "mysql8-192975d8aca-ecda66b852a2983": {"provider": "mysql", "driver": "mysql8", "name": "wp-engine ubuyfirst", "save-password": true, "configuration": {"host": "127.0.0.1", "port": "3306", "database": "wp_ubuyfirst", "url": "****************************************", "configurationType": "MANUAL", "home": "mysql_client", "type": "dev", "closeIdleConnection": true, "config-profile": "wp-engine", "properties": {"connectTimeout": "20000", "rewriteBatchedStatements": "true", "enabledTLSProtocols": "TLSv1,TLSv1.1,TLSv1.2,TLSv1.3"}, "auth-model": "native", "handlers": {"ssh_tunnel": {"type": "TUNNEL", "enabled": true}}}}, "postgres-jdbc-1964d1640e6-4bfeb44852fc520a": {"provider": "postgresql", "driver": "postgres-jdbc", "name": "wsapi-userdata", "save-password": true, "configuration": {"host": "userdata.cluster-ckjfvcdemxab.us-west-2.rds.amazonaws.com", "port": "5432", "database": "postgres", "url": "*****************************************************************************************", "configurationType": "MANUAL", "home": "postgresql_client", "type": "dev", "closeIdleConnection": true, "config-profile": "push.ubuyfirst.net", "provider-properties": {"@dbeaver-show-non-default-db@": "false", "@dbeaver-chosen-role@": ""}, "auth-model": "native", "handlers": {"ssh_tunnel": {"type": "TUNNEL", "enabled": true}}}}, "postgres-jdbc-19741126606-2005f45ffe8da144": {"provider": "postgresql", "driver": "postgres-jdbc", "name": "postgres", "save-password": true, "configuration": {"host": "localhost", "port": "5432", "database": "postgres", "url": "*****************************************", "configurationType": "MANUAL", "home": "postgresql-x64-17", "type": "dev", "closeIdleConnection": true, "provider-properties": {"@dbeaver-show-non-default-db@": "false", "@dbeaver-chosen-role@": ""}, "auth-model": "native"}}}, "virtual-models": {"postgres-jdbc-1917629ff22-a4d01436bbd82e": {"postgres": {"desktop_app": {":desktop_update_log": {"constraints": {"VIRTUAL_PK": {"type": "vpk", "attributes": ["hwid", "manual", "check_time"]}}}}}}, "mysql8-192975d8aca-ecda66b852a2983": {"wp_ubuyfirst": {":commit_to_buy_eias_token": {"constraints": {"VIRTUAL_PK": {"type": "vpk", "useAllColumns": true}}}}}}, "network-profiles": {"push.ubuyfirst.net": {"name": "push.ubuyfirst.net", "handlers": {"ssh_tunnel": {"type": "TUNNEL", "enabled": true, "save-password": true, "properties": {"host": "push.ubuyfirst.net", "port": 22.0, "authType": "PUBLIC_KEY", "keyPath": "C:\\ZOCS\\Visual Studio 2010\\Projects\\!EbayStuff\\AWS\\KeyPairs\\RomanskeypairAtDustinAws.pem", "implementation": "sshj", "shareTunnels": true, "localHost": "", "remoteHost": ""}}}}, "wp-engine": {"name": "wp-engine", "handlers": {"ssh_tunnel": {"type": "TUNNEL", "enabled": true, "save-password": true, "properties": {"host": "ubuyfirst.ssh.wpengine.net", "port": 22.0, "authType": "PUBLIC_KEY", "keyPath": "C:\\ZOCS\\Visual Studio 2010\\Projects\\!EbayStuff\\AWS\\wpengine\\wpengine_ed25519", "implementation": "sshj", "shareTunnels": true, "localHost": "", "remoteHost": ""}}}}}, "connection-types": {"dev": {"name": "Development", "color": "255,255,255", "description": "Regular development database", "auto-commit": true, "confirm-execute": false, "confirm-data-change": false, "smart-commit": false, "smart-commit-recover": true, "auto-close-transactions": true, "close-transactions-period": 1800, "auto-close-connections": true, "close-connections-period": 14400}}}