
UPDATE desktop_app.desktop_update_log
SET hwid = regexp_replace(hwid, '\s+', '+', 'g')
WHERE hwid ~ '\s';
WITH ranked_logs AS (
  SELECT 
    hwid,
    check_time,
    ROW_NUMBER() OVER (PARTITION BY hwid ORDER BY check_time DESC) as rn
  FROM 
    desktop_app.desktop_update_log
  WHERE 
    check_time < '2024-02-01' 
)
SELECT 
  hwid,
FROM ranked_logs
WHERE rn = 1
ORDER BY check_time;


-- Get the "last clicks" for each hwid before 2024-08-01
WITH ranked_clicks AS (
  SELECT
    hwid,
    action_time,
    ROW_NUMBER() OVER (PARTITION BY hwid ORDER BY action_time DESC) as rn
  FROM
    placeoffer.clicks
  WHERE
    action_time >'2024-07-01'
)
SELECT
  hwid,
  action_time AS most_recent_action_time
FROM ranked_clicks
WHERE rn = 1
ORDER BY action_time;


--most recent active hwids from update log
 WITH latest_check_times AS (
            SELECT
                hwid,
                MAX(check_time) AS most_recent_check_time
            FROM
                desktop_app.desktop_update_log dul 
            GROUP BY 
                hwid
        )
        SELECT
            hwid
        FROM 
            latest_check_times
        WHERE 
            most_recent_check_time > '2024-07-01'
        ORDER BY 
            hwid;

