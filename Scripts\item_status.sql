-- find top 100 frequent item ids, print count also
-- Certainly! To find the top 100 frequent item IDs along with their counts, you would typically need a table that contains item IDs and their occurrences. Since the provided schema does not include such a table, I'll assume you have a table named `items` with a column `item_id`. Here's how you could write the query:
SELECT "item_id", COUNT(*) AS "count"
FROM item_status.item_status_history ish 
where ish.status_changed_at > '2025-01-08 15:00'
GROUP BY "item_id"
ORDER BY "count" asc
LIMIT 1000
;
-- Please replace `"items"` and `"item_id"` with the actual table and column names you have in your database. If you have a different table structure, let me know, and I can adjust the query accordingly.
SELECT 
    "item_id", 
    COUNT(*) AS "count"
FROM 
    item_status.item_status_history ish
WHERE 
    ish.status_changed_at > '2025-01-08 15:00' and ish.variation_id =0
GROUP BY 
    "item_id"
HAVING 
    COUNT(*) > 2
ORDER BY 
    "count" ASC


SELECT current_status, COUNT(*) 
FROM item_status.item_status_current
GROUP by  current_status
ORDER BY current_status;

--country by day and status
SELECT 
    current_status, 
    DATE(last_updated) AS status_date, 
    COUNT(*) 
FROM 
    item_status.item_status_current
GROUP BY 
    current_status, 
    DATE(last_updated)
ORDER BY 
    current_status, 
    status_date;

   
--most occuring categories
SELECT 
    category_id , 
    COUNT(*) AS category_count
FROM 
    item_status.item_status_current
GROUP BY 
    category_id
ORDER BY 
    category_count DESC
LIMIT 1000;

--select unique item ids
select DISTINCT count(*) from item_status.item_status_current isc 
where variation_id = 0

--Status is the same
WITH cte AS (
    SELECT
        item_id,
        variation_id,
        status,
        status_changed_at,
        LAG(status) OVER (
            PARTITION BY item_id, variation_id 
            ORDER BY status_changed_at
        ) AS previous_status,
        LAG(status_changed_at) OVER (
            PARTITION BY item_id, variation_id 
            ORDER BY status_changed_at
        ) AS previous_status_changed_at
    FROM item_status.item_status_history
)
SELECT
    item_id,
    variation_id,
    status,
    status_changed_at,
    previous_status,
    previous_status_changed_at
FROM cte
WHERE status = previous_status and status_changed_at > '2025-01-08 15:00' and variation_id =0 and status > 0
ORDER BY item_id, variation_id, status_changed_at



--Status changed from one to another
SELECT h1.item_id, h1.variation_id, h1.status_changed_at AS temporarily_unavailable_at, h2.status_changed_at AS available_at
FROM item_status_history h1
JOIN item_status_history h2
  ON h1.item_id = h2.item_id
  AND h1.variation_id = h2.variation_id
  AND h1.status = 1  --0,UNAVAILABLE, 1 for 'TEMPORARILY_UNAVAILABLE', 2 for 'AVAILABLE'
  AND h2.status = 2  -- 
  AND h2.status_changed_at > h1.status_changed_at  
ORDER BY h1.status_changed_at DESC;

--From out to back in stock  in category id
SELECT h1.item_id, h1.variation_id, h1.status_changed_at AS temporarily_unavailable_at, h2.status_changed_at AS available_at
FROM item_status.item_status_history h1
JOIN item_status.item_status_history h2
  ON h1.item_id = h2.item_id
  AND h1.variation_id = h2.variation_id
  AND h1.status = 1  -- 'TEMPORARILY_UNAVAILABLE'
  AND h2.status = 2  -- 'AVAILABLE'
  AND h2.status_changed_at > h1.status_changed_at
JOIN item_status.item_status_current cur
  ON h1.item_id = cur.item_id
  AND h1.variation_id = cur.variation_id
--WHERE cur.category_id = 16204
ORDER BY h1.status_changed_at DESC;


--Most changed by category
  SELECT 
    ih.item_id, 
    ih.variation_id, 
    isc.seller_name, 
    isc.meta_category_id, 
    isc.category_id,
    COUNT(*) AS change_count
FROM 
    item_status_history ih
JOIN 
    item_status.item_status_current isc 
    ON ih.item_id = isc.item_id 
    AND ih.variation_id = isc.variation_id
    --where isc.category_id =16204
GROUP BY 
    ih.item_id, 
    ih.variation_id, 
    isc.seller_name, 
    isc.meta_category_id, 
    isc.category_id
HAVING 
    COUNT(*) > 1
ORDER BY 
    change_count DESC  -- Orders by the number of changes descending;

--detailed status changes
SELECT *
FROM (
    SELECT
        item_id,
        variation_id,
        status_changed_at,
        status AS new_status,
        LAG(status) OVER (PARTITION BY item_id, variation_id ORDER BY status_changed_at) AS previous_status
    FROM item_status_history
) subquery
WHERE new_status != previous_status
ORDER BY item_id, variation_id, status_changed_at;

select min(last_updated),max(last_updated) 
from item_status.item_status_current isc 









-- Table to store the current status of items with metadat

CREATE TABLE item_status_current (
    item_id int8,                       -- Base item ID
    variation_id int8,                  -- Variation ID (can be 0 if no variation)
    meta_category_id int4,              -- Meta Category ID
    category_id int4,                   -- Category ID
    seller_name VARCHAR(255),           -- Seller Username
    current_status int2,         -- Current status (available, temporarily_available, unavailable)
    last_updated TIMESTAMPTZ DEFAULT now(), -- Timestamp when the status was last updated
    PRIMARY KEY (item_id, variation_id)    -- Composite primary key (item_id + variation_id)
);

-- Table to store the history of status changes with metadata
CREATE TABLE item_status_history (
    item_id int8,                       -- Base item ID
    variation_id int8,                  -- Variation ID (can be 0 if no variation)
    status int2,                 -- Status (available, temporarily_available, unavailable)
    status_changed_at TIMESTAMPTZ,      -- Timestamp when the status changed
    PRIMARY KEY (item_id, variation_id, status_changed_at)
);

-- Indexes to optimize querying
CREATE INDEX idx_item_status_current_status ON item_status_current (current_status);
CREATE INDEX idx_item_status_current_last_updated ON item_status_current (last_updated);
CREATE INDEX idx_item_status_history_item_id ON item_status_history (item_id, variation_id);



SELECT rolname, rolsuper, rolreplication 
FROM pg_roles 
WHERE rolname = 'postgres';

GRANT rds_replication TO postgres ;