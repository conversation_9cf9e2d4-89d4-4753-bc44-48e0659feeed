SELECT search_name, item_id, category_id, title, price, shipping_price, shipping_type, shipping_cost_paid_by_option, dispatch_time_max, country_from, country_ships_to, postal_code, condition_id, duration_seconds, sub_title, condition_description, description, specifics, brand, model, mpn, upc, product_reference_id, seller_name, seller_feedback_count, seller_feedback_percent, seller_feedback_private, buyer_name, buyer_feedback_count, buyer_feedback_percent, buyer_feedback_private, buyer_protection, quantity_total, quantity_sold, lot_size, bid_count, picture_count, best_offer, get_it_fast, relist_parent_id, refund_option, returns_accepted_option, returns_within_option, revised, top_rated_listing, sold_as_bin, bin_sold, auction_sold, bin_not_sold, auction_not_sold, best_offer_sold, commit_to_buy, listing_type, pictures_url, time_posted, time_ended, updated_at
FROM data_analysis._2025_03_21_180250;

UPDATE data_analysis._2025_05_19_78192
SET pictures_url =
     '['
     || replace(
          regexp_replace(
            pictures_url,            -- your original column
            '([^;]+)',               -- capture each chunk
            '"\1"',                  -- wrap it in quotes
            'g'                      -- global replace
          ),
          ';', ','                  -- swap semicolons for commas
        )
     || ']';