-- Revoke all privileges from public schema
REVOKE ALL ON SCHEMA public FROM placeoffer_user;

-- Grant usage on placeoffer schema
GRANT USAGE ON SCHEMA placeoffer TO placeoffer_user;

-- <PERSON> select, insert, update, delete on all tables in placeoffer schema
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA placeoffer TO placeoffer_user;

-- Grant all privileges on all sequences in placeoffer schema
GRANT ALL ON ALL SEQUENCES IN SCHEMA placeoffer TO placeoffer_user;

-- Set default privileges for future tables in placeoffer schema
ALTER DEFAULT PRIVILEGES IN SCHEMA placeoffer
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO placeoffer_user;

-- Set default privileges for future sequences in placeoffer schema
ALTER DEFAULT PRIVILEGES IN SCHEMA placeoffer
GRANT ALL ON SEQUENCES TO placeoffer_user;

SELECT grantee, table_schema, privilege_type 
FROM information_schema.role_table_grants 
WHERE grantee = 'placeoffer_user';

ALTER USER placeoffer_user SET search_path TO placeoffer;

ALTER SCHEMA public OWNER TO pg_database_owner;
R<PERSON><PERSON><PERSON> CREATE ON SCHEMA public FROM PUBLIC;