SELECT *
FROM "user_actions"
WHERE "item_id" IN (
    SELECT DISTINCT "item_id"
    FROM "user_actions"
    WHERE "user_license" LIKE '3A3E%'
)
LIMIT 100;  




SELECT *
FROM "clicks_and_purchases"
WHERE "item_id" IN (
    SELECT DISTINCT "item_id"
    FROM "user_actions"
    WHERE "user_license" LIKE '3A3E%'
)
AND action_time > '2025-01-01'
ORDER BY "item_id", action_time 



SELECT *
FROM "clicks_and_purchases"
where  action_time > '2025-03-01'
and clicks_and_purchases.item_id in(
256842647552)
ORDER BY "item_id", action_time 


SELECT 
(EXTRACT(EPOCH FROM (cp.action_time - si.start_datetime))- si.found_time+1) AS "time_difference",
    "cp".*, 
    "si"."start_datetime"    
FROM "clicks_and_purchases" AS "cp"
JOIN "server_items" AS "si" ON "cp"."item_id" = "si"."item_id"
--ORDER BY "cp"."item_id"
where cp.action_time > '2024-03-01'
--and cp.license_key	 LIKE '3A3E%' 
and cp.license_key	 LIKE '6EA0-04FC%' 
and cp.action_type ='Browser'
--and cp.item_id in ()
--LIMIT 100; 