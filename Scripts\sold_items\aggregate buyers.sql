--group by buyer name in category
SELECT si.buyer_name, COUNT(*)
FROM public.sold_items si
WHERE leaf_category_id = 16204
GROUP BY si.buyer_name 
order by count desc

--group by seller with feedback count tolerance     
 WITH OrderedFeedback AS (
    select        
        buyer_name,
        buyer_feedback_count,
        LAG(buyer_feedback_count, 1, buyer_feedback_count) OVER (
            PARTITION BY buyer_name ORDER BY buyer_feedback_count
        ) AS prev_feedback_count
    FROM
        public.sold_items
    WHERE
        leaf_category_id = 177        AND 
        listing_status = 2 and
        buyer_name IS NOT NULL
        AND buyer_feedback_count IS NOT NULL
),
FeedbackDifferences AS (
    SELECT
        *,
        CASE
            WHEN prev_feedback_count = 0 THEN 0
            ELSE ABS(buyer_feedback_count - prev_feedback_count) / prev_feedback_count::float
        END AS percentage_difference,
        CASE
            WHEN prev_feedback_count = 0 THEN false            
            ELSE ABS(buyer_feedback_count - prev_feedback_count) / prev_feedback_count::float > 0.01 -- Adjust this threshold as needed
        END AS significant_change
    FROM OrderedFeedback
),
CumulativeGrouping AS (
    SELECT
        buyer_name,
        buyer_feedback_count,
        SUM(CASE WHEN significant_change THEN 1 ELSE 0 END) OVER (
            PARTITION BY buyer_name ORDER BY buyer_feedback_count
        ) AS group_id
    FROM FeedbackDifferences
)
SELECT
    buyer_name,   
    group_id,
    COUNT(*) AS total_entries,
    AVG(buyer_feedback_count) AS average_feedback_within_group
FROM
    CumulativeGrouping
GROUP BY
    buyer_name, group_id
ORDER BY
    total_entries desc

---

WITH OrderedFeedback AS (
    SELECT
        buyer_name,
        buyer_feedback_count,
        LAG(buyer_feedback_count, 1, buyer_feedback_count) OVER (
            PARTITION BY buyer_name ORDER BY buyer_feedback_count
        ) AS prev_feedback_count
    FROM
        public.sold_items
    WHERE
        leaf_category_id = 177 AND
        buyer_name IS NOT NULL AND
        buyer_feedback_count IS NOT NULL
),
FeedbackDifferences AS (
    SELECT
        buyer_name,
        buyer_feedback_count,
        prev_feedback_count,
        CASE
            WHEN prev_feedback_count = 0 THEN 1
            ELSE ABS(buyer_feedback_count - prev_feedback_count) / prev_feedback_count::float
        END > 0.01 AS significant_change
    FROM OrderedFeedback
),
GroupIDs AS (
    SELECT
        buyer_name,
        SUM(CASE WHEN significant_change THEN 1 ELSE 0 END) OVER (
            PARTITION BY buyer_name ORDER BY buyer_feedback_count ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) AS group_id
    FROM FeedbackDifferences
),
AggregatedValues AS (
    SELECT
        si.buyer_name,
        g.group_id,
        SUM(si.price * si.quantity_sold) AS total_value
    FROM
        public.sold_items si
        JOIN GroupIDs g ON si.buyer_name = g.buyer_name AND si.leaf_category_id = 16204
    WHERE
        si.buyer_name IS NOT NULL
    GROUP BY
        si.buyer_name, g.group_id
)
SELECT
    buyer_name,
    group_id,
    total_value
FROM
    AggregatedValues
ORDER BY
    total_value DESC;
   
   
    SELECT *
FROM sold_items si 
WHERE 
  (
    leaf_category_id = 31387 AND 
    seller_feedback_count > 600 AND 
    seller_feedback_count < 800 AND 
    seller_name ILIKE '%a%a%'
  ) 
  OR 
  (
    leaf_category_id = 31387 AND 
    buyer_feedback_count > 600 AND 
    buyer_feedback_count < 800 AND 
    buyer_name = 'aa'
  );

SELECT 
    date(fetch_time) as sale_day,
    COUNT(DISTINCT seller_name) as unique_sellers
FROM 
    sold_items
WHERE 
    fetch_time >= '2024-09-01'
GROUP BY 
    date(fetch_time)
ORDER BY 
    sale_day;
   