--Find sellers with specific feedback pattern
SELECT *
FROM sold_items si 
where seller_name LIKE '%aa';

--Complex seller feedback analysis over time periods
select
    si.item_id,
    si.seller_name,
    si.fetch_time,
    si.buyer_name,
    si.buyer_feedback_count
from
    sold_items si
where
    (si.seller_feedback_count >= 13640
        and si.seller_feedback_count <= 13669
        and si.fetch_time >= '2024-02-06 23:15:21Z'.<PERSON><PERSON>('Z')
        and si.fetch_time <= '2024-02-07 19:31:32Z'.<PERSON>m('Z')
        and seller_name like '%e%'
        and seller_name like '%g%')
    or (si.seller_feedback_count >= 13669
        and si.seller_feedback_count <= 13693
        and si.fetch_time >= '2024-02-07 19:31:32Z'.<PERSON><PERSON>('Z')
        and si.fetch_time <= '2024-02-08 18:34:04Z'.<PERSON><PERSON>('Z')
        and seller_name like '%e%'
        and seller_name like '%g%');
