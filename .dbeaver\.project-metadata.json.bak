{"resources": {"Scripts/EPN/Peter.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/Fragrancesnet-1.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/Fragrancesnet.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-1917629ff22-a4d01436bbd82e", "default-catalog": "postgres"}, "Scripts/ItemPricing/EPN.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/ItemPricing/buyers.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/ItemPricing/sold_items_and_epn.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/Larry issue.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/PlaceOffer/Script-2.sql": {"default-schema": "placeoffer", "default-datasource": "postgres-jdbc-1914fae26d1-7ede4a24713056f8", "default-catalog": "postgres"}, "Scripts/PlaceOffer/desktop_update_log.sql": {"default-schema": "desktop_app", "default-datasource": "postgres-jdbc-1914596aa90-29f70c8676716b7", "default-catalog": "postgres"}, "Scripts/PlaceOffer/update_log.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-1917629ff22-a4d01436bbd82e", "default-catalog": "postgres"}, "Scripts/Script-1.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/Script-2.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/Script-3.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/Script.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/clicks_and_purchases.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/commit_to_buy_log.sql": {"default-datasource": "mysql8-192975d8aca-ecda66b852a2983", "default-catalog": "wp_ubuyfirst"}, "Scripts/data_analysis_export.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/data_analysis_export2.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/desktop_update/update_log.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-1917629ff22-a4d01436bbd82e", "default-catalog": "postgres"}, "Scripts/item_status.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-1917629ff22-a4d01436bbd82e", "default-catalog": "postgres"}, "Scripts/server-items.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-18d2d848c32-2de084c5d26b2993", "default-catalog": "item_speed_db"}, "Scripts/server_items.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/sold_items/buyer_analysis.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/sold_items/cards.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/sold_items/cards_export.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/sold_items/epn_analysis.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/sold_items/seller_quantity_analysis.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/sold_items/time_analysis.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}, "Scripts/user-item-data-ddl.sql": {"default-schema": "public", "default-datasource": "postgres-jdbc-19282a6c266-506942572d949dd", "default-catalog": "postgres"}}}