-- Get the "last clicks" for each hwid before 2024-08-01
with ranked_clicks as (
select
	hwid,
	action_time,
	row_number() over (partition by hwid
order by
	action_time desc) as rn
from
	clicks_and_purchases cap
where
	action_time >'2024-07-01'
)
select
	hwid,
	action_time as most_recent_action_time
from
	ranked_clicks
where
	rn = 1
order by
	action_time;



SELECT DATE(action_time) AS action_date, COUNT(*) AS daily_count 
FROM clicks_and_purchases 
WHERE DATE(action_time) >= '2025-05-01'
and action_type ='Browser'
--and license_key ='AADA-AE87-F4DC-4B1C-9BDE-C4C8-98EF'
GROUP BY DATE(action_time) ORDER BY action_date;


SELECT * 
FROM clicks_and_purchases 
WHERE DATE(action_time) >= '2025-05-27'
and action_type ='Browser'
--and license_key ='AADA-AE87-F4DC-4B1C-9BDE-C4C8-98EF'
