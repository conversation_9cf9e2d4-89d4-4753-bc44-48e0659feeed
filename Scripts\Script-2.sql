SELECT search_name, COUNT(*) AS search_name_count
FROM data_analysis.item_predict ip
GROUP BY search_name
ORDER BY search_name_count DESC;


DO $$
DECLARE
    column_list TEXT := '';
    key_record RECORD;
    dynamic_query TEXT;
BEGIN
    -- Step 1: Find all unique keys in the specifics JSONB column
    FOR key_record IN 
        SELECT DISTINCT jsonb_object_keys(specifics) AS key
        FROM data_analysis.item_predict
    LOOP
        -- Step 2: Construct column expressions to extract each key as a separate column
        column_list := column_list || ', specifics->>' || quote_literal(key_record.key) || ' AS ' || quote_ident('specifics_' || key_record.key);
    END LOOP;

    -- Step 3: Construct the final query with dynamically created columns
    dynamic_query := 'CREATE TEMP TABLE temp_item_predict AS '
                     || 'SELECT search_name, item_id, category_id, title, price, product_reference_id'
                     || column_list
                     || ', shipping_price, shipping_type'
                     || ' FROM data_analysis.item_predict';

    -- Step 4: Execute the dynamic query to create the temp table
    EXECUTE dynamic_query;
END $$;

-- Step 5: Query the temporary table to get the results
SELECT * FROM temp_item_predict;



SELECT 
    c."id" AS "leafcategoryid",
    c."name_path" AS "category_name",
    COALESCE(feb.total_earnings, 0) AS "february22-28",
    COALESCE(mar.total_earnings, 0) AS "march1-11",
    COALESCE(mar.total_earnings, 0) - COALESCE(feb.total_earnings, 0) AS "difference"
FROM "categories" c
LEFT JOIN (
    SELECT "leafcategoryid", SUM("earnings") AS total_earnings
    FROM "epn_transactions"
    WHERE eventdate >= '2025-02-22' 
      AND eventdate < '2025-03-01'
    GROUP BY "leafcategoryid"
) feb ON c."id" = feb."leafcategoryid"
LEFT JOIN (
    SELECT "leafcategoryid", SUM("earnings") AS total_earnings
    FROM "epn_transactions"
    WHERE eventdate >= '2025-03-01' 
      AND eventdate < '2025-03-12'
    GROUP BY "leafcategoryid"
) mar ON c."id" = mar."leafcategoryid"
--ORDER BY COALESCE(mar.total_earnings, 0) desc
ORDER BY difference desc
LIMIT 500;


ORDER BY mar.total_earnings DESC
LIMIT 50;

SELECT "leafcategoryid", SUM("earnings") AS total_earnings
    FROM "epn_transactions"
    WHERE eventdate >= '2025-03-01' 
      AND eventdate < '2025-03-12'
    GROUP BY "leafcategoryid"


    
      SELECT "leafcategoryid", SUM("earnings") AS total_earnings
    FROM "epn_transactions"
    WHERE eventdate >= '2025-02-22' 
      AND eventdate < '2025-03-01'
    GROUP BY "leafcategoryid"
