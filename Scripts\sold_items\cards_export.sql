--export with specifics


select
  *,
  specifics->>'Sport' AS "Sport",
  specifics->>'Type' AS "Type",
  specifics->>'Season' AS "Season",
  specifics->>'PlayerAthlete' AS "PlayerAthlete",
  specifics->>'Manufacturer' AS "Manufacturer",
  specifics->>'Set' AS "Set",
  specifics->>'Team' AS "Team",
  specifics->>'League' AS "League",
  specifics->>'Card_Number' AS "Card_Number",
  specifics->>'Features' AS "Features",
  specifics->>'ParallelVariety' AS "ParallelVariety",
  specifics->>'Autographed' AS "Autographed",
  specifics->>'Year_Manufactured' AS "Year_Manufactured",
  specifics->>'OriginalLicensed_Reprint' AS "OriginalLicensed_Reprint",
  specifics->>'Card_Size' AS "Card_Size",
  specifics->>'Vintage' AS "Vintage",
  specifics->>'CountryRegion_of_Manufacture' AS "CountryRegion_of_Manufacture",
  specifics->>'Card_Name' AS "Card_Name",
  specifics->>'Language' AS "Language",
  specifics->>'Graded' AS "Graded",
  specifics->>'Material' AS "Material",
  specifics->>'Insert_Set' AS "Insert_Set",
  specifics->>'Print_Run' AS "Print_Run",
  specifics->>'Signed_By' AS "Signed_By",
  specifics->>'OriginalReprint' AS "OriginalReprint",
  specifics->>'Autograph_Authentication' AS "Autograph_Authentication",
  specifics->>'Product' AS "Product",
  specifics->>'Card_Manufacturer' AS "Card_Manufacturer",
  specifics->>'Autograph_Format' AS "Autograph_Format",
  specifics->>'Year' AS "Year",
  specifics->>'Card_Thickness' AS "Card_Thickness",
  specifics->>'Player' AS "Player",
  specifics->>'Grade' AS "Grade",
  specifics->>'EventTournament' AS "EventTournament",
  specifics->>'Professional_Grader' AS "Professional_Grader",
  specifics->>'Era' AS "Era",
  specifics->>'Card_Attributes' AS "Card_Attributes",
  specifics->>'Card_Condition' AS "Card_Condition",
  specifics->>'Franchise' AS "Franchise",
  specifics->>'Character' AS "Character",
  specifics->>'Game' AS "Game",
  specifics->>'Athlete' AS "Athlete",
  specifics->>'Series' AS "Series"
FROM "data_analysis"."_2025_02_05_261328_sport_cards";





SELECT
  *,
  specifics->>'Game' AS "Game",
  specifics->>'Card_Name' AS "Card_Name",
  specifics->>'Set' AS "Set",
  specifics->>'Card_Type' AS "Card_Type",
  specifics->>'Rarity' AS "Rarity",
  specifics->>'Card_Number' AS "Card_Number",
  specifics->>'Finish' AS "Finish",
  specifics->>'Manufacturer' AS "Manufacturer",
  specifics->>'Language' AS "Language",
  specifics->>'Character' AS "Character",
  specifics->>'Stage' AS "Stage",
  specifics->>'Features' AS "Features",
  specifics->>'HP' AS "HP",
  specifics->>'Speciality' AS "Speciality",
  specifics->>'Year_Manufactured' AS "Year_Manufactured",
  specifics->>'Illustrator' AS "Illustrator",
  specifics->>'Autographed' AS "Autographed",
  specifics->>'AttributeMTGColor' AS "AttributeMTGColor",
  specifics->>'Card_Size' AS "Card_Size",
  specifics->>'Material' AS "Material",
  specifics->>'Age_Level' AS "Age_Level",
  specifics->>'CountryRegion_of_Manufacture' AS "CountryRegion_of_Manufacture",
  specifics->>'CreatureMonster_Type' AS "CreatureMonster_Type",
  specifics->>'Vintage' AS "Vintage",
  specifics->>'Graded' AS "Graded",
  specifics->>'AttributeMTGColour' AS "AttributeMTGColour",
  specifics->>'Type' AS "Type",
  specifics->>'Card_Condition' AS "Card_Condition",
  specifics->>'AttackPower' AS "AttackPower",
  specifics->>'ConventionEvent' AS "ConventionEvent",
  specifics->>'Cost' AS "Cost",
  specifics->>'Grade' AS "Grade",
  specifics->>'Franchise' AS "Franchise",
  specifics->>'DefenseToughness' AS "DefenseToughness",
  specifics->>'Professional_Grader' AS "Professional_Grader",
  specifics->>'Character_Family' AS "Character_Family",
  specifics->>'Edition' AS "Edition"
FROM "data_analysis"."_2025_02_04_183454_ccg";
